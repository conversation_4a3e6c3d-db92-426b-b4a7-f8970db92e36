@echo off
chcp 65001 >nul
title نظام نقاط البيع - مقهى البلايستيشن

echo.
echo ========================================
echo 🎮 نظام نقاط البيع - مقهى البلايستيشن
echo PlayStation Cafe POS System
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo ❌ Python is not installed
    echo.
    echo 💡 يرجى تحميل وتثبيت Python من:
    echo Please download and install Python from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo Found Python installation
python --version

echo.
echo 🔍 فحص المكتبات المطلوبة...
echo Checking required dependencies...

REM محاولة تشغيل البرنامج
python run.py
if errorlevel 1 (
    echo.
    echo ⚠️ يبدو أن هناك مكتبات مفقودة
    echo It seems some dependencies are missing
    echo.
    echo ❓ هل تريد تثبيت المكتبات المطلوبة؟
    echo Do you want to install required dependencies?
    echo.
    set /p choice="اكتب y للموافقة أو n للرفض (y/n): "
    
    if /i "%choice%"=="y" (
        echo.
        echo 📦 تثبيت المكتبات...
        echo Installing dependencies...
        python install.py
        
        echo.
        echo 🔄 محاولة تشغيل البرنامج مرة أخرى...
        echo Trying to run the program again...
        python run.py
    ) else (
        echo.
        echo ℹ️ يمكنك تثبيت المكتبات لاحقاً باستخدام:
        echo You can install dependencies later using:
        echo python install.py
    )
)

echo.
pause
