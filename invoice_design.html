<!-- تبويب تصميم الفاتورة -->
<section id="invoice-design" class="page-section">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-paint-brush"></i>
                تصميم وتخصيص الفاتورة
            </h2>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="showTab('design-layout')">
                <i class="fas fa-layout"></i>
                تخطيط الفاتورة
            </div>
            <div class="tab" onclick="showTab('design-content')">
                <i class="fas fa-edit"></i>
                محتوى الفاتورة
            </div>
            <div class="tab" onclick="showTab('design-style')">
                <i class="fas fa-palette"></i>
                الألوان والخطوط
            </div>
            <div class="tab" onclick="showTab('design-preview')">
                <i class="fas fa-eye"></i>
                معاينة
            </div>
        </div>

        <!-- تخطيط الفاتورة -->
        <div id="design-layout" class="tab-content active">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-ruler"></i>
                        أبعاد الفاتورة
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label">نوع الورق:</label>
                        <select class="form-control" id="paperType">
                            <option value="thermal_58">حراري 58 مم</option>
                            <option value="thermal_70">حراري 70 مم</option>
                            <option value="thermal_80" selected>حراري 80 مم</option>
                            <option value="a4">A4 عادي</option>
                            <option value="custom">مخصص</option>
                        </select>
                    </div>

                    <div class="form-group" id="customDimensions" style="display: none;">
                        <label class="form-label">العرض (مم):</label>
                        <input type="number" class="form-control" id="customWidth" value="80">
                        
                        <label class="form-label" style="margin-top: 10px;">الطول (مم):</label>
                        <input type="number" class="form-control" id="customHeight" value="200">
                    </div>

                    <div class="form-group">
                        <label class="form-label">الهوامش:</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <input type="number" class="form-control" placeholder="أعلى" id="marginTop" value="5">
                            <input type="number" class="form-control" placeholder="أسفل" id="marginBottom" value="5">
                            <input type="number" class="form-control" placeholder="يمين" id="marginRight" value="3">
                            <input type="number" class="form-control" placeholder="يسار" id="marginLeft" value="3">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">اتجاه النص:</label>
                        <select class="form-control" id="textDirection">
                            <option value="rtl" selected>من اليمين لليسار (عربي)</option>
                            <option value="ltr">من اليسار لليمين (إنجليزي)</option>
                        </select>
                    </div>
                </div>

                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-list"></i>
                        ترتيب العناصر
                    </h3>
                    
                    <div id="elementsOrder" class="sortable-list">
                        <div class="sortable-item" data-element="logo">
                            <i class="fas fa-grip-vertical"></i>
                            <i class="fas fa-image"></i>
                            شعار المحل
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="sortable-item" data-element="shop-info">
                            <i class="fas fa-grip-vertical"></i>
                            <i class="fas fa-store"></i>
                            معلومات المحل
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="sortable-item" data-element="invoice-info">
                            <i class="fas fa-grip-vertical"></i>
                            <i class="fas fa-receipt"></i>
                            معلومات الفاتورة
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="sortable-item" data-element="items-table">
                            <i class="fas fa-grip-vertical"></i>
                            <i class="fas fa-table"></i>
                            جدول الأصناف
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="sortable-item" data-element="total">
                            <i class="fas fa-grip-vertical"></i>
                            <i class="fas fa-calculator"></i>
                            المجموع الكلي
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="sortable-item" data-element="footer">
                            <i class="fas fa-grip-vertical"></i>
                            <i class="fas fa-comment"></i>
                            رسالة الختام
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوى الفاتورة -->
        <div id="design-content" class="tab-content">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-store"></i>
                        معلومات المحل
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label">اسم المحل:</label>
                        <input type="text" class="form-control" id="shopName" value="مقهى البلايستيشن">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">العنوان:</label>
                        <textarea class="form-control" id="shopAddress" rows="2">الرياض - المملكة العربية السعودية</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">رقم الهاتف:</label>
                        <input type="text" class="form-control" id="shopPhone" value="+966 11 234 5678">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني:</label>
                        <input type="email" class="form-control" id="shopEmail" value="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">الموقع الإلكتروني:</label>
                        <input type="url" class="form-control" id="shopWebsite" value="">
                    </div>
                </div>

                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-receipt"></i>
                        تفاصيل الفاتورة
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label">عنوان الفاتورة:</label>
                        <input type="text" class="form-control" id="invoiceTitle" value="فاتورة مبيعات">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">بادئة رقم الفاتورة:</label>
                        <input type="text" class="form-control" id="invoicePrefix" value="INV">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">تنسيق التاريخ:</label>
                        <select class="form-control" id="dateFormat">
                            <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
                            <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                            <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                            <option value="arabic" selected>التاريخ الهجري</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">عناوين الأعمدة:</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <input type="text" class="form-control" placeholder="الصنف" id="colItem" value="الصنف">
                            <input type="text" class="form-control" placeholder="السعر" id="colPrice" value="السعر">
                            <input type="text" class="form-control" placeholder="الكمية" id="colQty" value="الكمية">
                            <input type="text" class="form-control" placeholder="المجموع" id="colTotal" value="المجموع">
                        </div>
                    </div>
                </div>
            </div>

            <div class="card" style="margin-top: 20px;">
                <h3 class="card-title">
                    <i class="fas fa-comment"></i>
                    الرسائل والملاحظات
                </h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div>
                        <div class="form-group">
                            <label class="form-label">رسالة الترحيب:</label>
                            <textarea class="form-control" id="welcomeMessage" rows="2">أهلاً وسهلاً بكم في مقهى البلايستيشن</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">رسالة الختام:</label>
                            <textarea class="form-control" id="footerMessage" rows="3">نسعد بخدمتكم
شكراً لزيارتكم
نتطلع لرؤيتكم مرة أخرى</textarea>
                        </div>
                    </div>
                    
                    <div>
                        <div class="form-group">
                            <label class="form-label">معلومات إضافية:</label>
                            <textarea class="form-control" id="additionalInfo" rows="3">ساعات العمل: 24/7
خدمة التوصيل متاحة
WiFi مجاني</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">شروط وأحكام:</label>
                            <textarea class="form-control" id="termsConditions" rows="2">لا يمكن استرداد المبلغ بعد الدفع
يرجى الاحتفاظ بالفاتورة</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الألوان والخطوط -->
        <div id="design-style" class="tab-content">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-font"></i>
                        إعدادات الخطوط
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label">خط العناوين:</label>
                        <select class="form-control" id="headerFont">
                            <option value="Arial">Arial</option>
                            <option value="Cairo" selected>Cairo</option>
                            <option value="Amiri">Amiri</option>
                            <option value="Tajawal">Tajawal</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">حجم خط العنوان الرئيسي:</label>
                        <input type="range" class="form-control" id="titleFontSize" min="16" max="32" value="24">
                        <span id="titleFontSizeValue">24px</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">خط المحتوى:</label>
                        <select class="form-control" id="contentFont">
                            <option value="Arial" selected>Arial</option>
                            <option value="Cairo">Cairo</option>
                            <option value="Courier New">Courier New</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">حجم خط المحتوى:</label>
                        <input type="range" class="form-control" id="contentFontSize" min="8" max="16" value="12">
                        <span id="contentFontSizeValue">12px</span>
                    </div>
                </div>

                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-palette"></i>
                        نظام الألوان
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label">لون الخلفية:</label>
                        <input type="color" class="form-control" id="backgroundColor" value="#ffffff">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">لون النص الرئيسي:</label>
                        <input type="color" class="form-control" id="textColor" value="#000000">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">لون العناوين:</label>
                        <input type="color" class="form-control" id="headerColor" value="#1a1a2e">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">لون الحدود:</label>
                        <input type="color" class="form-control" id="borderColor" value="#cccccc">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">لون المجموع:</label>
                        <input type="color" class="form-control" id="totalColor" value="#00d4aa">
                    </div>
                </div>
            </div>

            <div class="card" style="margin-top: 20px;">
                <h3 class="card-title">
                    <i class="fas fa-image"></i>
                    الشعار والصور
                </h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div>
                        <div class="form-group">
                            <label class="form-label">شعار المحل:</label>
                            <input type="file" class="form-control" id="logoFile" accept="image/*">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">حجم الشعار:</label>
                            <select class="form-control" id="logoSize">
                                <option value="small">صغير (40px)</option>
                                <option value="medium" selected>متوسط (60px)</option>
                                <option value="large">كبير (80px)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">موضع الشعار:</label>
                            <select class="form-control" id="logoPosition">
                                <option value="center" selected>وسط</option>
                                <option value="right">يمين</option>
                                <option value="left">يسار</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <div class="form-group">
                            <label class="form-label">صورة خلفية (اختياري):</label>
                            <input type="file" class="form-control" id="backgroundImage" accept="image/*">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">شفافية الخلفية:</label>
                            <input type="range" class="form-control" id="backgroundOpacity" min="0" max="100" value="10">
                            <span id="backgroundOpacityValue">10%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معاينة -->
        <div id="design-preview" class="tab-content">
            <div style="display: grid; grid-template-columns: 1fr 400px; gap: 30px;">
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-eye"></i>
                        معاينة الفاتورة
                    </h3>
                    
                    <div class="preview-container" id="invoicePreview">
                        <!-- سيتم إنشاء المعاينة هنا -->
                    </div>
                </div>

                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-cog"></i>
                        إعدادات المعاينة
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label">مقياس العرض:</label>
                        <input type="range" class="form-control" id="previewScale" min="50" max="150" value="100">
                        <span id="previewScaleValue">100%</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">بيانات تجريبية:</label>
                        <label class="switch">
                            <input type="checkbox" id="useSampleData" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="updatePreview()">
                            <i class="fas fa-sync"></i>
                            تحديث المعاينة
                        </button>
                        
                        <button class="btn btn-success" onclick="saveDesign()" style="margin-top: 10px; width: 100%;">
                            <i class="fas fa-save"></i>
                            حفظ التصميم
                        </button>
                        
                        <button class="btn btn-info" onclick="exportTemplate()" style="margin-top: 10px; width: 100%;">
                            <i class="fas fa-download"></i>
                            تصدير القالب
                        </button>
                        
                        <button class="btn btn-warning" onclick="resetDesign()" style="margin-top: 10px; width: 100%;">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* أنماط تبويب تصميم الفاتورة */
.sortable-list {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 15px;
    min-height: 300px;
}

.sortable-item {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: move;
    transition: all 0.3s ease;
}

.sortable-item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.sortable-item i.fa-grip-vertical {
    color: #999;
    cursor: grab;
}

.sortable-item i.fa-grip-vertical:active {
    cursor: grabbing;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: auto;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--success);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.preview-container {
    background: #f5f5f5;
    border: 2px solid #ddd;
    border-radius: 10px;
    padding: 20px;
    min-height: 500px;
    overflow: auto;
}

.form-control[type="color"] {
    height: 40px;
    padding: 5px;
}

.form-control[type="range"] {
    margin-bottom: 5px;
}
</style>
