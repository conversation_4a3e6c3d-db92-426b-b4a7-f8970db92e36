# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['start_pos_final.py'],
    pathex=[],
    binaries=[],
    datas=[('web_interface.html', '.'), ('app.js', '.'), ('invoice_template.html', '.'), ('advanced_features.js', '.'), ('invoice_design.html', '.'), ('printer_settings.html', '.')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='نظام_نقاط_البيع',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
