<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة الأصناف</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .item-card {
            border: 2px solid #ddd;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            cursor: pointer;
            display: inline-block;
            width: 200px;
            text-align: center;
        }
        .item-card.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .invoice-table th, .invoice-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        .invoice-table th {
            background: #f8f9fa;
        }
        #debug {
            background: #f8f8f8;
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار إضافة الأصناف</h1>
        
        <div id="debug">
            <strong>معلومات التصحيح:</strong><br>
            <span id="debugInfo">لم يتم اختيار أي صنف</span>
        </div>
        
        <h3>الأصناف المتاحة:</h3>
        <div id="itemsContainer">
            <div class="item-card" onclick="selectItem({id: 1, name: 'قهوة عربية', price: 15})">
                <h4>قهوة عربية</h4>
                <p>15 ريال</p>
            </div>
            <div class="item-card" onclick="selectItem({id: 2, name: 'شاي أحمر', price: 10})">
                <h4>شاي أحمر</h4>
                <p>10 ريال</p>
            </div>
            <div class="item-card" onclick="selectItem({id: 3, name: 'كابتشينو', price: 20})">
                <h4>كابتشينو</h4>
                <p>20 ريال</p>
            </div>
        </div>
        
        <div class="controls">
            <input type="number" id="quantity" value="1" min="1" style="width: 60px; padding: 5px;">
            <button class="btn btn-success" onclick="addToInvoice()">إضافة للفاتورة</button>
            <button class="btn" onclick="clearInvoice()">مسح الفاتورة</button>
        </div>
        
        <h3>الفاتورة الحالية:</h3>
        <table class="invoice-table">
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody id="invoiceItems">
                <!-- سيتم إضافة الأصناف هنا -->
            </tbody>
        </table>
        
        <div style="text-align: center; margin-top: 20px;">
            <strong>المجموع الكلي: <span id="totalAmount">0.00 ريال</span></strong>
        </div>
    </div>

    <script>
        let selectedItem = null;
        let currentInvoice = {
            items: [],
            total: 0
        };

        function selectItem(item) {
            selectedItem = item;
            
            // تمييز الصنف المختار
            document.querySelectorAll('.item-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.item-card').classList.add('selected');
            
            // تحديث معلومات التصحيح
            document.getElementById('debugInfo').innerHTML = 
                `تم اختيار: ${item.name} - ${item.price} ريال (ID: ${item.id})`;
        }

        function addToInvoice() {
            console.log('addToInvoice called');
            console.log('selectedItem:', selectedItem);
            
            if (!selectedItem) {
                alert('يرجى اختيار صنف أولاً');
                return;
            }
            
            const quantity = parseInt(document.getElementById('quantity').value) || 1;
            const subtotal = selectedItem.price * quantity;
            
            console.log('quantity:', quantity);
            console.log('subtotal:', subtotal);
            
            // البحث عن الصنف في الفاتورة
            const existingItemIndex = currentInvoice.items.findIndex(item => item.id === selectedItem.id);
            
            if (existingItemIndex > -1) {
                // تحديث الكمية
                currentInvoice.items[existingItemIndex].quantity += quantity;
                currentInvoice.items[existingItemIndex].subtotal = 
                    currentInvoice.items[existingItemIndex].price * currentInvoice.items[existingItemIndex].quantity;
            } else {
                // إضافة صنف جديد
                currentInvoice.items.push({
                    id: selectedItem.id,
                    name: selectedItem.name,
                    price: selectedItem.price,
                    quantity: quantity,
                    subtotal: subtotal
                });
            }
            
            console.log('currentInvoice.items:', currentInvoice.items);
            
            updateInvoiceDisplay();
            alert(`تم إضافة ${selectedItem.name} للفاتورة`);
        }

        function updateInvoiceDisplay() {
            console.log('updateInvoiceDisplay called');
            
            const invoiceItems = document.getElementById('invoiceItems');
            if (!invoiceItems) {
                console.error('invoiceItems element not found');
                return;
            }
            
            invoiceItems.innerHTML = '';
            currentInvoice.total = 0;
            
            currentInvoice.items.forEach((item, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.name}</td>
                    <td>${item.price.toFixed(2)} ريال</td>
                    <td>${item.quantity}</td>
                    <td>${item.subtotal.toFixed(2)} ريال</td>
                `;
                
                invoiceItems.appendChild(row);
                currentInvoice.total += item.subtotal;
            });
            
            // تحديث المجموع
            const totalElement = document.getElementById('totalAmount');
            if (totalElement) {
                totalElement.textContent = `${currentInvoice.total.toFixed(2)} ريال`;
            }
            
            console.log('Invoice updated, total:', currentInvoice.total);
        }

        function clearInvoice() {
            currentInvoice.items = [];
            currentInvoice.total = 0;
            updateInvoiceDisplay();
        }
    </script>
</body>
</html>
