<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 نظام نقاط البيع - مقهى البلايستيشن</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #1a1a2e;
            --secondary: #16213e;
            --accent: #0f3460;
            --success: #00d4aa;
            --warning: #ffb347;
            --danger: #ff6b6b;
            --info: #4ecdc4;
            --light: #ffffff;
            --dark: #0a0a0a;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
            --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --border-radius: 15px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            overflow-x: hidden;
            direction: rtl;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* الشريط الجانبي */
        .sidebar {
            width: 280px;
            background: var(--primary);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            transition: var(--transition);
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 30px 20px;
            text-align: center;
            background: var(--gradient-primary);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-success);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 2rem;
            animation: pulse 2s infinite;
            position: relative;
            z-index: 1;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .app-title {
            color: var(--light);
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
        }

        .app-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            position: relative;
            z-index: 1;
        }

        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            margin: 5px 15px;
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition);
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-success);
            transition: var(--transition);
            z-index: -1;
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            right: 0;
        }

        .nav-link:hover,
        .nav-link.active {
            color: var(--light);
            transform: translateX(-5px);
        }

        .nav-icon {
            margin-left: 15px;
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        /* المحتوى الرئيسي */
        .main-content {
            flex: 1;
            margin-right: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            min-height: 100vh;
            transition: var(--transition);
        }

        .header {
            background: var(--light);
            padding: 20px 30px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: var(--transition);
            transform: translate(-50%, -50%);
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--light);
        }

        .btn-success {
            background: var(--gradient-success);
            color: var(--light);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .content-area {
            padding: 30px;
        }

        .page-section {
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .page-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            background: var(--light);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* شبكة الإحصائيات */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--light);
            padding: 25px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: var(--transition);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-success);
        }

        .stat-card:hover {
            transform: scale(1.05);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        /* نموذج الفاتورة */
        .invoice-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .items-section {
            background: var(--light);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow);
        }

        .invoice-section {
            background: var(--light);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary);
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
            background: var(--light);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--info);
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }

        .search-box {
            position: relative;
            margin-bottom: 20px;
        }

        .search-box input {
            padding-right: 45px;
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .items-list {
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #f0f0f0;
            border-radius: var(--border-radius);
            background: var(--light);
        }

        .item-card {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-card:hover {
            background: #f8f9fa;
            transform: translateX(-5px);
        }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .item-details {
            font-size: 0.9rem;
            color: #666;
        }

        .item-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--success);
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }

        .quantity-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: var(--light);
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .quantity-btn:hover {
            transform: scale(1.1);
        }

        .quantity-input {
            width: 80px;
            text-align: center;
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* جدول الفاتورة */
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: var(--light);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .invoice-table th {
            background: var(--gradient-primary);
            color: var(--light);
            padding: 15px;
            text-align: center;
            font-weight: 600;
        }

        .invoice-table td {
            padding: 12px 15px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .invoice-table tr:hover {
            background: #f8f9fa;
        }

        .total-section {
            background: var(--gradient-primary);
            color: var(--light);
            padding: 25px;
            border-radius: var(--border-radius);
            text-align: center;
            margin: 20px 0;
        }

        .total-label {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .total-amount {
            font-size: 2.5rem;
            font-weight: 700;
        }

        /* تبويبات */
        .tabs {
            display: flex;
            background: var(--light);
            border-radius: var(--border-radius);
            padding: 5px;
            margin-bottom: 25px;
            box-shadow: var(--shadow);
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-weight: 600;
            color: #666;
        }

        .tab.active {
            background: var(--gradient-primary);
            color: var(--light);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                width: 100%;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .invoice-form {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .header-title {
                font-size: 1.4rem;
            }
        }

        /* تأثيرات إضافية */
        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .glow {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background: var(--light);
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* الإشعارات */
        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 10001;
            pointer-events: none;
        }

        .notification-item {
            background: var(--success);
            color: var(--light);
            padding: 15px 25px;
            border-radius: var(--border-radius);
            margin-bottom: 10px;
            box-shadow: var(--shadow);
            animation: slideInLeft 0.3s ease-out;
            pointer-events: auto;
            cursor: pointer;
        }

        .notification-item.warning {
            background: var(--warning);
        }

        .notification-item.danger {
            background: var(--danger);
        }

        .notification-item.info {
            background: var(--info);
        }

        @keyframes slideInLeft {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* جداول محسنة */
        .table-container {
            overflow-x: auto;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .invoice-table {
            background: var(--light);
            border-radius: var(--border-radius);
        }

        .invoice-table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: var(--transition);
        }

        /* أزرار محسنة */
        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.875rem;
        }

        .btn-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }

        /* بطاقات محسنة */
        .feature-card {
            background: var(--light);
            border-radius: var(--border-radius);
            padding: 25px;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: var(--accent);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* شريط التقدم */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--gradient-success);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* تبديل الوضع */
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--gradient-primary);
            border: none;
            color: var(--light);
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: var(--shadow);
            transition: var(--transition);
            z-index: 1000;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        /* تحسينات للجوال */
        @media (max-width: 768px) {
            .modal-content {
                margin: 20px;
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                justify-content: center;
            }

            .feature-card {
                padding: 20px;
            }

            .theme-toggle {
                bottom: 80px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }

        /* تأثيرات خاصة */
        .sparkle {
            position: relative;
            overflow: hidden;
        }

        .sparkle::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: sparkle 2s infinite;
            pointer-events: none;
        }

        @keyframes sparkle {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        /* حالات التحميل */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
        }

        @keyframes skeleton-loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* أنماط تبويبات التصميم والطباعة */
        .receipt-type-card {
            background: white;
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .receipt-type-card:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transform: translateY(-5px);
        }

        .receipt-type-card.active {
            border-color: var(--success);
            background: #f0fff4;
        }

        .receipt-icon {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 15px;
        }

        .receipt-specs {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: right;
        }

        .receipt-specs div {
            margin-bottom: 5px;
            font-size: 0.9rem;
            color: #666;
        }

        /* مفاتيح التبديل */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* أشرطة التمرير المخصصة */
        .form-control[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            height: 8px;
            background: #ddd;
            border-radius: 4px;
            outline: none;
        }

        .form-control[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: var(--success);
            border-radius: 50%;
            cursor: pointer;
        }

        .form-control[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: var(--success);
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }

        /* أنماط اختيار الألوان */
        .form-control[type="color"] {
            width: 100%;
            height: 50px;
            border: 2px solid #ddd;
            border-radius: var(--border-radius);
            cursor: pointer;
            padding: 5px;
        }

        .form-control[type="color"]:hover {
            border-color: var(--info);
        }

        /* معاينة التصميم */
        #designPreview {
            transition: all 0.3s ease;
        }

        /* نتائج اختبار الطباعة */
        #printTestResults {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        #printTestResults::-webkit-scrollbar {
            width: 8px;
        }

        #printTestResults::-webkit-scrollbar-track {
            background: #333;
        }

        #printTestResults::-webkit-scrollbar-thumb {
            background: #0f0;
            border-radius: 4px;
        }

        /* أنماط عناصر الطابعة */
        .printer-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .printer-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .printer-item.selected {
            border-color: var(--success);
            background: #f0fff4;
        }

        .printer-item.default {
            border-color: var(--info);
            background: #f0f8ff;
        }

        .printer-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .printer-status.online {
            background: var(--success);
            color: white;
        }

        .printer-status.offline {
            background: var(--danger);
            color: white;
        }

        /* تحسينات الطباعة */
        @media print {
            .sidebar,
            .header,
            .btn,
            .modal,
            .notification,
            .theme-toggle {
                display: none !important;
            }

            .main-content {
                margin-right: 0;
            }

            .card {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- الشريط الجانبي -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo floating">🎮</div>
                <h1 class="app-title">نظام نقاط البيع</h1>
                <p class="app-subtitle">مقهى البلايستيشن</p>
            </div>
            
            <div class="nav-menu">
                <div class="nav-item">
                    <a href="#dashboard" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        لوحة التحكم
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#invoice" class="nav-link" data-page="invoice">
                        <i class="fas fa-receipt nav-icon"></i>
                        إصدار فاتورة
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#items" class="nav-link" data-page="items">
                        <i class="fas fa-box nav-icon"></i>
                        إدارة الأصناف
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#reports" class="nav-link" data-page="reports">
                        <i class="fas fa-chart-bar nav-icon"></i>
                        التقارير
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#settings" class="nav-link" data-page="settings">
                        <i class="fas fa-cog nav-icon"></i>
                        الإعدادات
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#invoice-design" class="nav-link" data-page="invoice-design">
                        <i class="fas fa-paint-brush nav-icon"></i>
                        تصميم الفاتورة
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#printers" class="nav-link" data-page="printers">
                        <i class="fas fa-print nav-icon"></i>
                        إعدادات الطباعة
                    </a>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <header class="header">
                <h1 class="header-title">
                    <i class="fas fa-bars" id="menuToggle" style="cursor: pointer; display: none;"></i>
                    <span id="pageTitle">لوحة التحكم</span>
                </h1>
                <div class="header-actions">
                    <span id="currentTime"></span>
                    <button class="btn btn-primary" onclick="showNotification('مرحباً بك!', 'success')">
                        <i class="fas fa-bell"></i>
                        الإشعارات
                    </button>
                </div>
            </header>

            <div class="content-area">
                <!-- لوحة التحكم -->
                <section id="dashboard" class="page-section active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">📄</div>
                            <div class="stat-value" id="todayInvoices">0</div>
                            <div class="stat-label">فواتير اليوم</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-value" id="todaySales">0.00</div>
                            <div class="stat-label">مبيعات اليوم (ريال)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📦</div>
                            <div class="stat-value" id="totalItems">0</div>
                            <div class="stat-label">إجمالي الأصناف</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📊</div>
                            <div class="stat-value" id="avgInvoice">0.00</div>
                            <div class="stat-label">متوسط الفاتورة</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i class="fas fa-chart-line"></i>
                                نظرة عامة سريعة
                            </h2>
                        </div>
                        <p>مرحباً بك في نظام نقاط البيع المتقدم. يمكنك من هنا إدارة جميع عمليات المقهى بسهولة وفعالية.</p>
                        <div style="margin-top: 20px;">
                            <button class="btn btn-success" onclick="showPage('invoice')">
                                <i class="fas fa-plus"></i>
                                إصدار فاتورة جديدة
                            </button>
                            <button class="btn btn-primary" onclick="showPage('items')" style="margin-right: 10px;">
                                <i class="fas fa-box"></i>
                                إدارة الأصناف
                            </button>
                        </div>
                    </div>
                </section>

                <!-- إصدار فاتورة -->
                <section id="invoice" class="page-section">
                    <div class="invoice-form">
                        <div class="items-section">
                            <h3 class="card-title">
                                <i class="fas fa-shopping-cart"></i>
                                الأصناف المتاحة
                            </h3>
                            
                            <div class="search-box">
                                <input type="text" class="form-control" placeholder="البحث في الأصناف..." id="itemSearch">
                                <i class="fas fa-search search-icon"></i>
                            </div>

                            <div class="quantity-controls">
                                <button class="quantity-btn" onclick="changeQuantity(-1)">-</button>
                                <input type="number" class="form-control quantity-input" value="1" min="1" id="quantity">
                                <button class="quantity-btn" onclick="changeQuantity(1)">+</button>
                                <button class="btn btn-success" onclick="addToInvoice()" style="margin-right: 10px;">
                                    <i class="fas fa-plus"></i>
                                    إضافة
                                </button>
                            </div>

                            <div class="items-list" id="itemsList">
                                <!-- سيتم تحميل الأصناف هنا -->
                            </div>
                        </div>

                        <div class="invoice-section">
                            <h3 class="card-title">
                                <i class="fas fa-receipt"></i>
                                الفاتورة الحالية
                            </h3>

                            <div class="form-group">
                                <label class="form-label">رقم الفاتورة:</label>
                                <input type="text" class="form-control" id="invoiceNumber" readonly>
                            </div>

                            <table class="invoice-table" id="invoiceTable">
                                <thead>
                                    <tr>
                                        <th>الصنف</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المجموع</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceItems">
                                    <!-- سيتم إضافة الأصناف هنا -->
                                </tbody>
                            </table>

                            <div class="total-section">
                                <div class="total-label">المجموع الكلي</div>
                                <div class="total-amount" id="totalAmount">0.00 ريال</div>
                            </div>

                            <div style="display: flex; gap: 10px; margin-top: 20px;">
                                <button class="btn btn-success" onclick="saveInvoice()">
                                    <i class="fas fa-save"></i>
                                    حفظ
                                </button>
                                <button class="btn btn-primary" onclick="printInvoice()">
                                    <i class="fas fa-print"></i>
                                    طباعة
                                </button>
                                <button class="btn btn-warning" onclick="newInvoice()">
                                    <i class="fas fa-file"></i>
                                    جديد
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- إدارة الأصناف -->
                <section id="items" class="page-section">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i class="fas fa-box"></i>
                                إدارة الأصناف
                            </h2>
                            <button class="btn btn-success" onclick="showAddItemModal()">
                                <i class="fas fa-plus"></i>
                                إضافة صنف جديد
                            </button>
                        </div>

                        <div class="search-box">
                            <input type="text" class="form-control" placeholder="البحث في الأصناف..." id="itemsSearch">
                            <i class="fas fa-search search-icon"></i>
                        </div>

                        <table class="invoice-table" id="itemsTable">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>السعر</th>
                                    <th>الفئة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTableBody">
                                <!-- سيتم تحميل الأصناف هنا -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- التقارير -->
                <section id="reports" class="page-section">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i class="fas fa-chart-bar"></i>
                                التقارير والإحصائيات
                            </h2>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">📊</div>
                                <div class="stat-value" id="totalSales">0.00</div>
                                <div class="stat-label">إجمالي المبيعات</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">📈</div>
                                <div class="stat-value" id="monthlyGrowth">0%</div>
                                <div class="stat-label">النمو الشهري</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">🏆</div>
                                <div class="stat-value" id="topItem">-</div>
                                <div class="stat-label">الصنف الأكثر مبيعاً</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">⭐</div>
                                <div class="stat-value" id="avgRating">5.0</div>
                                <div class="stat-label">تقييم الخدمة</div>
                            </div>
                        </div>

                        <div class="card">
                            <h3 class="card-title">
                                <i class="fas fa-history"></i>
                                آخر الفواتير
                            </h3>
                            <table class="invoice-table" id="recentInvoicesTable">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="recentInvoicesBody">
                                    <!-- سيتم تحميل الفواتير هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- الإعدادات -->
                <section id="settings" class="page-section">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i class="fas fa-cog"></i>
                                إعدادات النظام
                            </h2>
                        </div>

                        <div class="tabs">
                            <div class="tab active" onclick="showTab('general-settings')">
                                <i class="fas fa-sliders-h"></i>
                                عام
                            </div>
                            <div class="tab" onclick="showTab('shop-settings')">
                                <i class="fas fa-store"></i>
                                المحل
                            </div>
                            <div class="tab" onclick="showTab('system-settings')">
                                <i class="fas fa-desktop"></i>
                                النظام
                            </div>
                        </div>

                        <div id="general-settings" class="tab-content active">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                                <div>
                                    <div class="form-group">
                                        <label class="form-label">اسم المحل:</label>
                                        <input type="text" class="form-control" id="settingsShopName" value="مقهى البلايستيشن">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">عنوان المحل:</label>
                                        <textarea class="form-control" id="settingsShopAddress" rows="3">الرياض - المملكة العربية السعودية</textarea>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">رقم الهاتف:</label>
                                        <input type="tel" class="form-control" id="settingsShopPhone" value="+966 11 234 5678">
                                    </div>
                                </div>
                                <div>
                                    <div class="form-group">
                                        <label class="form-label">البريد الإلكتروني:</label>
                                        <input type="email" class="form-control" id="settingsShopEmail" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">رسالة الختام:</label>
                                        <textarea class="form-control" id="settingsFooterMessage" rows="3">نسعد بخدمتكم
شكراً لزيارتكم</textarea>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">العملة:</label>
                                        <select class="form-control" id="settingsCurrency">
                                            <option value="ريال" selected>ريال سعودي</option>
                                            <option value="درهم">درهم إماراتي</option>
                                            <option value="دينار">دينار كويتي</option>
                                            <option value="دولار">دولار أمريكي</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <button class="btn btn-success" onclick="saveSettings()">
                                <i class="fas fa-save"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </section>

                <!-- تصميم الفاتورة -->
                <section id="invoice-design" class="page-section">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i class="fas fa-paint-brush"></i>
                                تصميم وتخصيص الفاتورة
                            </h2>
                        </div>

                        <div class="tabs">
                            <div class="tab active" onclick="showDesignTab('design-layout')">
                                <i class="fas fa-layout"></i>
                                تخطيط الفاتورة
                            </div>
                            <div class="tab" onclick="showDesignTab('design-content')">
                                <i class="fas fa-edit"></i>
                                محتوى الفاتورة
                            </div>
                            <div class="tab" onclick="showDesignTab('design-style')">
                                <i class="fas fa-palette"></i>
                                الألوان والخطوط
                            </div>
                            <div class="tab" onclick="showDesignTab('design-preview')">
                                <i class="fas fa-eye"></i>
                                معاينة
                            </div>
                        </div>

                        <!-- تخطيط الفاتورة -->
                        <div id="design-layout" class="tab-content active">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-ruler"></i>
                                        أبعاد الفاتورة
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">نوع الورق:</label>
                                        <select class="form-control" id="paperType">
                                            <option value="thermal_58">حراري 58 مم</option>
                                            <option value="thermal_70">حراري 70 مم</option>
                                            <option value="thermal_80" selected>حراري 80 مم</option>
                                            <option value="a4">A4 عادي</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">اتجاه النص:</label>
                                        <select class="form-control" id="textDirection">
                                            <option value="rtl" selected>من اليمين لليسار (عربي)</option>
                                            <option value="ltr">من اليسار لليمين (إنجليزي)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-cog"></i>
                                        إعدادات التخطيط
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">عرض الفاتورة:</label>
                                        <input type="range" class="form-control" id="invoiceWidth" min="200" max="800" value="400">
                                        <span id="invoiceWidthValue">400px</span>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">تباعد الأسطر:</label>
                                        <input type="range" class="form-control" id="lineHeight" min="1" max="3" value="1.5" step="0.1">
                                        <span id="lineHeightValue">1.5</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- محتوى الفاتورة -->
                        <div id="design-content" class="tab-content">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-store"></i>
                                        معلومات المحل
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">اسم المحل:</label>
                                        <input type="text" class="form-control" id="designShopName" value="مقهى البلايستيشن">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">العنوان:</label>
                                        <textarea class="form-control" id="designShopAddress" rows="2">الرياض - المملكة العربية السعودية</textarea>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">رقم الهاتف:</label>
                                        <input type="text" class="form-control" id="designShopPhone" value="+966 11 234 5678">
                                    </div>
                                </div>

                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-comment"></i>
                                        الرسائل
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">رسالة الترحيب:</label>
                                        <textarea class="form-control" id="welcomeMsg" rows="2">أهلاً وسهلاً بكم</textarea>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">رسالة الختام:</label>
                                        <textarea class="form-control" id="footerMsg" rows="3">نسعد بخدمتكم
شكراً لزيارتكم</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الألوان والخطوط -->
                        <div id="design-style" class="tab-content">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-font"></i>
                                        إعدادات الخطوط
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">حجم خط العنوان:</label>
                                        <input type="range" class="form-control" id="titleFontSize" min="16" max="32" value="24">
                                        <span id="titleFontSizeValue">24px</span>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">حجم خط المحتوى:</label>
                                        <input type="range" class="form-control" id="contentFontSize" min="10" max="18" value="14">
                                        <span id="contentFontSizeValue">14px</span>
                                    </div>
                                </div>

                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-palette"></i>
                                        نظام الألوان
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">لون الخلفية:</label>
                                        <input type="color" class="form-control" id="bgColor" value="#ffffff">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">لون النص:</label>
                                        <input type="color" class="form-control" id="textColor" value="#000000">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">لون العناوين:</label>
                                        <input type="color" class="form-control" id="headerColor" value="#1a1a2e">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معاينة -->
                        <div id="design-preview" class="tab-content">
                            <div style="display: grid; grid-template-columns: 1fr 300px; gap: 30px;">
                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-eye"></i>
                                        معاينة الفاتورة
                                    </h3>

                                    <div id="designPreview" style="border: 2px solid #ddd; border-radius: 10px; padding: 20px; background: #f5f5f5; min-height: 400px;">
                                        <!-- سيتم إنشاء المعاينة هنا -->
                                    </div>
                                </div>

                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-cog"></i>
                                        إعدادات المعاينة
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">مقياس العرض:</label>
                                        <input type="range" class="form-control" id="previewScale" min="50" max="150" value="100">
                                        <span id="previewScaleValue">100%</span>
                                    </div>

                                    <div style="margin-top: 20px;">
                                        <button class="btn btn-primary" onclick="updateDesignPreview()" style="width: 100%; margin-bottom: 10px;">
                                            <i class="fas fa-sync"></i>
                                            تحديث المعاينة
                                        </button>

                                        <button class="btn btn-success" onclick="saveInvoiceDesign()" style="width: 100%; margin-bottom: 10px;">
                                            <i class="fas fa-save"></i>
                                            حفظ التصميم
                                        </button>

                                        <button class="btn btn-warning" onclick="resetInvoiceDesign()" style="width: 100%;">
                                            <i class="fas fa-undo"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- إعدادات الطباعة -->
                <section id="printers" class="page-section">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i class="fas fa-print"></i>
                                إعدادات الطباعة والطابعات
                            </h2>
                        </div>

                        <div class="tabs">
                            <div class="tab active" onclick="showPrinterTab('printer-selection')">
                                <i class="fas fa-printer"></i>
                                اختيار الطابعة
                            </div>
                            <div class="tab" onclick="showPrinterTab('print-options')">
                                <i class="fas fa-cog"></i>
                                خيارات الطباعة
                            </div>
                            <div class="tab" onclick="showPrinterTab('receipt-types')">
                                <i class="fas fa-receipt"></i>
                                أنواع الفواتير
                            </div>
                        </div>

                        <!-- اختيار الطابعة -->
                        <div id="printer-selection" class="tab-content active">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-list"></i>
                                        الطابعات المتاحة
                                    </h3>

                                    <div class="form-group">
                                        <button class="btn btn-info" onclick="refreshPrintersList()">
                                            <i class="fas fa-sync"></i>
                                            تحديث قائمة الطابعات
                                        </button>
                                    </div>

                                    <div id="availablePrinters" style="border: 2px solid #f0f0f0; border-radius: 10px; padding: 15px; max-height: 300px; overflow-y: auto;">
                                        <!-- سيتم تحميل الطابعات هنا -->
                                    </div>
                                </div>

                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-star"></i>
                                        الطابعة الافتراضية
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">الطابعة المحددة:</label>
                                        <select class="form-control" id="defaultPrinterSelect">
                                            <option value="">اختر طابعة...</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">نوع الفاتورة الافتراضي:</label>
                                        <select class="form-control" id="defaultReceiptTypeSelect">
                                            <option value="thermal_58">حراري 58 مم</option>
                                            <option value="thermal_70">حراري 70 مم</option>
                                            <option value="thermal_80" selected>حراري 80 مم</option>
                                            <option value="a4">A4 عادي</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">طباعة تلقائية عند الحفظ:</label>
                                        <label class="switch">
                                            <input type="checkbox" id="autoPrintCheck">
                                            <span class="slider"></span>
                                        </label>
                                    </div>

                                    <button class="btn btn-success" onclick="savePrinterSettings()">
                                        <i class="fas fa-save"></i>
                                        حفظ الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- خيارات الطباعة -->
                        <div id="print-options" class="tab-content">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-sliders-h"></i>
                                        إعدادات الطباعة العامة
                                    </h3>

                                    <div class="form-group">
                                        <label class="form-label">جودة الطباعة:</label>
                                        <select class="form-control" id="printQualitySelect">
                                            <option value="draft">مسودة (سريع)</option>
                                            <option value="normal" selected>عادي</option>
                                            <option value="high">عالي الجودة</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">عدد النسخ:</label>
                                        <input type="number" class="form-control" id="printCopiesInput" value="1" min="1" max="5">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">قطع الورق التلقائي:</label>
                                        <label class="switch">
                                            <input type="checkbox" id="autoCutCheck" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="card">
                                    <h3 class="card-title">
                                        <i class="fas fa-vial"></i>
                                        اختبار الطباعة
                                    </h3>

                                    <div class="form-group">
                                        <button class="btn btn-primary" onclick="testPrint()">
                                            <i class="fas fa-play"></i>
                                            تشغيل اختبار الطباعة
                                        </button>
                                    </div>

                                    <div class="form-group">
                                        <button class="btn btn-info" onclick="printTestPage()">
                                            <i class="fas fa-print"></i>
                                            طباعة صفحة اختبار
                                        </button>
                                    </div>

                                    <div id="printTestResults" style="background: #000; color: #0f0; font-family: monospace; padding: 10px; border-radius: 5px; height: 150px; overflow-y: auto; margin-top: 10px;">
                                        <!-- نتائج الاختبار -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أنواع الفواتير -->
                        <div id="receipt-types" class="tab-content">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                                <div class="receipt-type-card" data-type="thermal_58" onclick="selectReceiptTypeCard('thermal_58')">
                                    <div class="receipt-icon">📄</div>
                                    <h4>حراري 58 مم</h4>
                                    <div class="receipt-specs">
                                        <div>العرض: 58 مم</div>
                                        <div>الأحرف: 32 حرف/سطر</div>
                                        <div>الاستخدام: طابعات صغيرة</div>
                                    </div>
                                </div>

                                <div class="receipt-type-card" data-type="thermal_70" onclick="selectReceiptTypeCard('thermal_70')">
                                    <div class="receipt-icon">📄</div>
                                    <h4>حراري 70 مم</h4>
                                    <div class="receipt-specs">
                                        <div>العرض: 70 مم</div>
                                        <div>الأحرف: 38 حرف/سطر</div>
                                        <div>الاستخدام: طابعات متوسطة</div>
                                    </div>
                                </div>

                                <div class="receipt-type-card active" data-type="thermal_80" onclick="selectReceiptTypeCard('thermal_80')">
                                    <div class="receipt-icon">📄</div>
                                    <h4>حراري 80 مم</h4>
                                    <div class="receipt-specs">
                                        <div>العرض: 80 مم</div>
                                        <div>الأحرف: 42 حرف/سطر</div>
                                        <div>الاستخدام: الأكثر شيوعاً</div>
                                    </div>
                                </div>

                                <div class="receipt-type-card" data-type="a4" onclick="selectReceiptTypeCard('a4')">
                                    <div class="receipt-icon">📋</div>
                                    <h4>A4 عادي</h4>
                                    <div class="receipt-specs">
                                        <div>العرض: 210 مم</div>
                                        <div>الأحرف: 80 حرف/سطر</div>
                                        <div>الاستخدام: طابعات عادية</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- نافذة الإشعارات -->
    <div id="notification" class="notification"></div>

    <!-- نافذة إضافة صنف -->
    <div id="addItemModal" class="modal">
        <div class="modal-content">
            <h3>إضافة صنف جديد</h3>
            <div class="form-group">
                <label class="form-label">اسم الصنف:</label>
                <input type="text" class="form-control" id="newItemName">
            </div>
            <div class="form-group">
                <label class="form-label">السعر:</label>
                <input type="number" class="form-control" id="newItemPrice" step="0.01">
            </div>
            <div class="form-group">
                <label class="form-label">الفئة:</label>
                <select class="form-control" id="newItemCategory">
                    <option value="مشروبات">مشروبات</option>
                    <option value="طعام">طعام</option>
                    <option value="ألعاب">ألعاب</option>
                    <option value="حلويات">حلويات</option>
                    <option value="وجبات خفيفة">وجبات خفيفة</option>
                </select>
            </div>
            <div style="display: flex; gap: 10px; margin-top: 20px;">
                <button class="btn btn-success" onclick="addNewItem()">
                    <i class="fas fa-plus"></i>
                    إضافة
                </button>
                <button class="btn btn-secondary" onclick="closeAddItemModal()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script src="advanced_features.js"></script>

    <script>
        // تحميل المحتوى الإضافي
        document.addEventListener('DOMContentLoaded', function() {
            loadAdditionalContent();
        });

        function loadAdditionalContent() {
            // تهيئة التبويبات والميزات المتقدمة
            initializeDesignFeatures();
            initializePrinterFeatures();
        }

        // تهيئة ميزات التصميم
        function initializeDesignFeatures() {
            // إعداد أشرطة التمرير
            const sliders = [
                { id: 'invoiceWidth', valueId: 'invoiceWidthValue', suffix: 'px' },
                { id: 'lineHeight', valueId: 'lineHeightValue', suffix: '' },
                { id: 'titleFontSize', valueId: 'titleFontSizeValue', suffix: 'px' },
                { id: 'contentFontSize', valueId: 'contentFontSizeValue', suffix: 'px' },
                { id: 'previewScale', valueId: 'previewScaleValue', suffix: '%' }
            ];

            sliders.forEach(slider => {
                const element = document.getElementById(slider.id);
                const valueElement = document.getElementById(slider.valueId);

                if (element && valueElement) {
                    element.addEventListener('input', function() {
                        valueElement.textContent = this.value + slider.suffix;
                        updateDesignPreview();
                    });
                }
            });

            // إعداد حقول النص
            const textFields = ['designShopName', 'designShopAddress', 'designShopPhone', 'welcomeMsg', 'footerMsg'];
            textFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('input', updateDesignPreview);
                }
            });

            // إعداد حقول الألوان
            const colorFields = ['bgColor', 'textColor', 'headerColor'];
            colorFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('change', updateDesignPreview);
                }
            });

            // تحديث المعاينة الأولية
            updateDesignPreview();
        }

        // تهيئة ميزات الطباعة
        function initializePrinterFeatures() {
            refreshPrintersList();
        }

        // عرض تبويب التصميم
        function showDesignTab(tabId) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('#invoice-design .tab-content').forEach(content => {
                content.classList.remove('active');
            });

            document.querySelectorAll('#invoice-design .tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض التبويب المحدد
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // تفعيل زر التبويب
            event.target.classList.add('active');

            if (tabId === 'design-preview') {
                updateDesignPreview();
            }
        }

        // عرض تبويب الطباعة
        function showPrinterTab(tabId) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('#printers .tab-content').forEach(content => {
                content.classList.remove('active');
            });

            document.querySelectorAll('#printers .tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض التبويب المحدد
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // تفعيل زر التبويب
            event.target.classList.add('active');
        }

        // تحديث معاينة التصميم
        function updateDesignPreview() {
            const preview = document.getElementById('designPreview');
            if (!preview) return;

            const settings = {
                shopName: document.getElementById('designShopName')?.value || 'مقهى البلايستيشن',
                shopAddress: document.getElementById('designShopAddress')?.value || 'الرياض - المملكة العربية السعودية',
                shopPhone: document.getElementById('designShopPhone')?.value || '+966 11 234 5678',
                welcomeMsg: document.getElementById('welcomeMsg')?.value || 'أهلاً وسهلاً بكم',
                footerMsg: document.getElementById('footerMsg')?.value || 'نسعد بخدمتكم',
                titleFontSize: document.getElementById('titleFontSize')?.value || 24,
                contentFontSize: document.getElementById('contentFontSize')?.value || 14,
                bgColor: document.getElementById('bgColor')?.value || '#ffffff',
                textColor: document.getElementById('textColor')?.value || '#000000',
                headerColor: document.getElementById('headerColor')?.value || '#1a1a2e',
                invoiceWidth: document.getElementById('invoiceWidth')?.value || 400,
                lineHeight: document.getElementById('lineHeight')?.value || 1.5
            };

            const scale = document.getElementById('previewScale')?.value || 100;

            preview.innerHTML = `
                <div style="
                    width: ${settings.invoiceWidth}px;
                    background: ${settings.bgColor};
                    color: ${settings.textColor};
                    font-size: ${settings.contentFontSize}px;
                    line-height: ${settings.lineHeight};
                    padding: 20px;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    text-align: center;
                    transform: scale(${scale / 100});
                    transform-origin: top right;
                    margin: 0 auto;
                ">
                    <h2 style="color: ${settings.headerColor}; font-size: ${settings.titleFontSize}px; margin-bottom: 10px;">
                        ${settings.shopName}
                    </h2>
                    <p style="margin-bottom: 5px;">${settings.shopAddress}</p>
                    <p style="margin-bottom: 15px;">${settings.shopPhone}</p>

                    <div style="border-top: 1px solid ${settings.textColor}; border-bottom: 1px solid ${settings.textColor}; padding: 10px 0; margin: 15px 0;">
                        <p>${settings.welcomeMsg}</p>
                        <p><strong>رقم الفاتورة:</strong> INV-20250108-001</p>
                        <p><strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    </div>

                    <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                        <thead>
                            <tr style="border-bottom: 1px solid ${settings.textColor};">
                                <th style="padding: 5px;">الصنف</th>
                                <th style="padding: 5px;">السعر</th>
                                <th style="padding: 5px;">الكمية</th>
                                <th style="padding: 5px;">المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 3px;">قهوة عربية</td>
                                <td style="padding: 3px;">8.00</td>
                                <td style="padding: 3px;">1</td>
                                <td style="padding: 3px;">8.00</td>
                            </tr>
                            <tr>
                                <td style="padding: 3px;">شاي أحمر</td>
                                <td style="padding: 3px;">5.00</td>
                                <td style="padding: 3px;">2</td>
                                <td style="padding: 3px;">10.00</td>
                            </tr>
                        </tbody>
                    </table>

                    <div style="border-top: 1px solid ${settings.textColor}; padding-top: 10px; margin-top: 15px;">
                        <h3 style="color: ${settings.headerColor};">المجموع الكلي: 18.00 ريال</h3>
                    </div>

                    <div style="margin-top: 20px; font-style: italic;">
                        ${settings.footerMsg}
                    </div>
                </div>
            `;
        }

        // حفظ تصميم الفاتورة
        function saveInvoiceDesign() {
            const designSettings = {
                shopName: document.getElementById('designShopName')?.value,
                shopAddress: document.getElementById('designShopAddress')?.value,
                shopPhone: document.getElementById('designShopPhone')?.value,
                welcomeMsg: document.getElementById('welcomeMsg')?.value,
                footerMsg: document.getElementById('footerMsg')?.value,
                titleFontSize: document.getElementById('titleFontSize')?.value,
                contentFontSize: document.getElementById('contentFontSize')?.value,
                bgColor: document.getElementById('bgColor')?.value,
                textColor: document.getElementById('textColor')?.value,
                headerColor: document.getElementById('headerColor')?.value,
                invoiceWidth: document.getElementById('invoiceWidth')?.value,
                lineHeight: document.getElementById('lineHeight')?.value
            };

            localStorage.setItem('invoiceDesign', JSON.stringify(designSettings));

            sendToServer('save_design', designSettings)
                .then(result => {
                    if (result.success) {
                        showNotification('تم حفظ تصميم الفاتورة بنجاح', 'success');
                    } else {
                        showNotification('فشل في حفظ التصميم', 'danger');
                    }
                });
        }

        // إعادة تعيين تصميم الفاتورة
        function resetInvoiceDesign() {
            if (confirm('هل تريد إعادة تعيين جميع إعدادات التصميم؟')) {
                document.getElementById('designShopName').value = 'مقهى البلايستيشن';
                document.getElementById('designShopAddress').value = 'الرياض - المملكة العربية السعودية';
                document.getElementById('designShopPhone').value = '+966 11 234 5678';
                document.getElementById('welcomeMsg').value = 'أهلاً وسهلاً بكم';
                document.getElementById('footerMsg').value = 'نسعد بخدمتكم';
                document.getElementById('titleFontSize').value = 24;
                document.getElementById('contentFontSize').value = 14;
                document.getElementById('bgColor').value = '#ffffff';
                document.getElementById('textColor').value = '#000000';
                document.getElementById('headerColor').value = '#1a1a2e';
                document.getElementById('invoiceWidth').value = 400;
                document.getElementById('lineHeight').value = 1.5;

                // تحديث قيم العرض
                document.getElementById('titleFontSizeValue').textContent = '24px';
                document.getElementById('contentFontSizeValue').textContent = '14px';
                document.getElementById('invoiceWidthValue').textContent = '400px';
                document.getElementById('lineHeightValue').textContent = '1.5';

                updateDesignPreview();
                showNotification('تم إعادة تعيين التصميم', 'info');
            }
        }

        // تحديث قائمة الطابعات
        function refreshPrintersList() {
            const printersContainer = document.getElementById('availablePrinters');
            const printerSelect = document.getElementById('defaultPrinterSelect');

            if (!printersContainer || !printerSelect) return;

            // بيانات تجريبية للطابعات
            const printers = [
                { name: 'طابعة حرارية - USB', status: 'online', isDefault: true },
                { name: 'Microsoft Print to PDF', status: 'online', isDefault: false },
                { name: 'طابعة الشبكة', status: 'offline', isDefault: false }
            ];

            // عرض الطابعات
            printersContainer.innerHTML = '';
            printerSelect.innerHTML = '<option value="">اختر طابعة...</option>';

            printers.forEach((printer, index) => {
                // إضافة للقائمة
                const printerDiv = document.createElement('div');
                printerDiv.className = `printer-item ${printer.isDefault ? 'default' : ''}`;
                printerDiv.innerHTML = `
                    <div>
                        <strong>${printer.name}</strong>
                        ${printer.isDefault ? '<br><small>الطابعة الافتراضية</small>' : ''}
                    </div>
                    <div class="printer-status ${printer.status}">
                        ${printer.status === 'online' ? 'متصل' : 'غير متصل'}
                    </div>
                `;
                printerDiv.onclick = () => selectPrinter(index, printer.name);
                printersContainer.appendChild(printerDiv);

                // إضافة للقائمة المنسدلة
                const option = document.createElement('option');
                option.value = printer.name;
                option.textContent = printer.name;
                if (printer.isDefault) {
                    option.selected = true;
                }
                printerSelect.appendChild(option);
            });

            showNotification('تم تحديث قائمة الطابعات', 'info');
        }

        // اختيار طابعة
        function selectPrinter(index, printerName) {
            document.querySelectorAll('.printer-item').forEach(item => {
                item.classList.remove('selected');
            });

            document.querySelectorAll('.printer-item')[index].classList.add('selected');
            document.getElementById('defaultPrinterSelect').value = printerName;
        }

        // اختيار نوع الفاتورة
        function selectReceiptTypeCard(type) {
            document.querySelectorAll('.receipt-type-card').forEach(card => {
                card.classList.remove('active');
            });

            document.querySelector(`[data-type="${type}"]`).classList.add('active');
            document.getElementById('defaultReceiptTypeSelect').value = type;

            const typeNames = {
                'thermal_58': 'حراري 58 مم',
                'thermal_70': 'حراري 70 مم',
                'thermal_80': 'حراري 80 مم',
                'a4': 'A4 عادي'
            };

            showNotification(`تم اختيار نوع الفاتورة: ${typeNames[type]}`, 'success');
        }

        // حفظ إعدادات الطباعة
        function savePrinterSettings() {
            const settings = {
                defaultPrinter: document.getElementById('defaultPrinterSelect')?.value,
                defaultReceiptType: document.getElementById('defaultReceiptTypeSelect')?.value,
                autoPrint: document.getElementById('autoPrintCheck')?.checked,
                printQuality: document.getElementById('printQualitySelect')?.value,
                printCopies: document.getElementById('printCopiesInput')?.value,
                autoCut: document.getElementById('autoCutCheck')?.checked
            };

            localStorage.setItem('printerSettings', JSON.stringify(settings));

            sendToServer('save_printer_settings', settings)
                .then(result => {
                    if (result.success) {
                        showNotification('تم حفظ إعدادات الطباعة بنجاح', 'success');
                    } else {
                        showNotification('فشل في حفظ الإعدادات', 'danger');
                    }
                });
        }

        // اختبار الطباعة
        function testPrint() {
            const resultsDiv = document.getElementById('printTestResults');
            if (resultsDiv) {
                resultsDiv.innerHTML += `[${new Date().toLocaleTimeString()}] بدء اختبار الطباعة...\n`;
                resultsDiv.scrollTop = resultsDiv.scrollHeight;

                setTimeout(() => {
                    resultsDiv.innerHTML += `[${new Date().toLocaleTimeString()}] اختبار الاتصال... ✅\n`;
                    resultsDiv.innerHTML += `[${new Date().toLocaleTimeString()}] اختبار الطباعة... ✅\n`;
                    resultsDiv.innerHTML += `[${new Date().toLocaleTimeString()}] اكتمل الاختبار بنجاح\n\n`;
                    resultsDiv.scrollTop = resultsDiv.scrollHeight;
                }, 1000);
            }

            showNotification('جاري تشغيل اختبار الطباعة...', 'info');
        }

        // طباعة صفحة اختبار
        function printTestPage() {
            const testContent = `
                <div style="text-align: center; font-family: Arial; padding: 20px;">
                    <h2>🎮 صفحة اختبار الطباعة</h2>
                    <p>مقهى البلايستيشن</p>
                    <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>الوقت: ${new Date().toLocaleTimeString('ar-SA')}</p>
                    <hr>
                    <p>هذه صفحة اختبار للتأكد من عمل الطابعة بشكل صحيح</p>
                    <p>إذا ظهرت هذه الرسالة فإن الطابعة تعمل بشكل سليم</p>
                    <hr>
                    <p>شكراً لاستخدام نظام نقاط البيع</p>
                </div>
            `;

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>صفحة اختبار الطباعة</title>
                    <style>body { font-family: Arial; direction: rtl; }</style>
                </head>
                <body>${testContent}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();

            showNotification('تم إرسال صفحة الاختبار للطباعة', 'success');
        }

        // وظائف إضافية
        function showAddItemModal() {
            document.getElementById('addItemModal').classList.add('show');
        }

        function closeAddItemModal() {
            document.getElementById('addItemModal').classList.remove('show');
            // مسح الحقول
            document.getElementById('newItemName').value = '';
            document.getElementById('newItemPrice').value = '';
            document.getElementById('newItemCategory').value = 'مشروبات';
        }

        function addNewItem() {
            const name = document.getElementById('newItemName').value.trim();
            const price = parseFloat(document.getElementById('newItemPrice').value);
            const category = document.getElementById('newItemCategory').value;

            if (!name || !price || price <= 0) {
                showNotification('يرجى إدخال بيانات صحيحة', 'warning');
                return;
            }

            const newItem = { name, price, category };

            sendToServer('add_item', newItem)
                .then(result => {
                    if (result.success) {
                        showNotification('تم إضافة الصنف بنجاح', 'success');
                        closeAddItemModal();
                        loadItems(); // إعادة تحميل الأصناف
                    } else {
                        showNotification('فشل في إضافة الصنف', 'danger');
                    }
                });
        }

        function saveSettings() {
            const settings = {
                shop_name: document.getElementById('settingsShopName').value,
                shop_address: document.getElementById('settingsShopAddress').value,
                shop_phone: document.getElementById('settingsShopPhone').value,
                shop_email: document.getElementById('settingsShopEmail').value,
                footer_message: document.getElementById('settingsFooterMessage').value,
                currency: document.getElementById('settingsCurrency').value
            };

            sendToServer('save_settings', settings)
                .then(result => {
                    if (result.success) {
                        showNotification('تم حفظ الإعدادات بنجاح', 'success');
                    } else {
                        showNotification('فشل في حفظ الإعدادات', 'danger');
                    }
                });
        }
    </script>
</body>
</html>
