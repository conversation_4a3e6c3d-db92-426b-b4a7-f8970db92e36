#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الفاتورة النظيفة بدون المعلومات الإضافية
Test Clean Invoice without Extra Information

فكرة وتنفيذ: المهندس سيف رافع
هذا البرنامج مجاني لوجه الله تعالى
"""

import os
import json
import webbrowser
import threading
import time
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler

class CleanInvoiceHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path == '/':
            self.path = '/web_interface.html'
        elif self.path.startswith('/api/'):
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            if self.path == '/api/data':
                data = {
                    'items': [
                        {'id': 1, 'name': 'ساعة بلايستيشن', 'price': 15.0, 'category': 'ألعاب'},
                        {'id': 2, 'name': 'قهوة عربية', 'price': 8.0, 'category': 'مشروبات'},
                        {'id': 3, 'name': 'شاي أحمر', 'price': 5.0, 'category': 'مشروبات'},
                        {'id': 4, 'name': 'عصير برتقال', 'price': 10.0, 'category': 'مشروبات'},
                        {'id': 5, 'name': 'ساندويش تونة', 'price': 20.0, 'category': 'طعام'},
                        {'id': 6, 'name': 'بيتزا صغيرة', 'price': 35.0, 'category': 'طعام'},
                        {'id': 7, 'name': 'مياه معدنية', 'price': 2.0, 'category': 'مشروبات'},
                        {'id': 8, 'name': 'كولا', 'price': 6.0, 'category': 'مشروبات'},
                        {'id': 9, 'name': 'شيبس', 'price': 4.0, 'category': 'وجبات خفيفة'},
                        {'id': 10, 'name': 'شوكولاتة', 'price': 7.0, 'category': 'حلويات'}
                    ],
                    'stats': {
                        'today_invoices': 25,
                        'today_sales': 750.80,
                        'total_items': 10,
                        'avg_invoice': 30.03
                    },
                    'settings': {
                        'shop_name': 'مقهى البلايستيشن',
                        'shop_address': 'الرياض - المملكة العربية السعودية',
                        'shop_phone': '+966 11 234 5678',
                        'shop_email': '<EMAIL>',
                        'footer_message': 'نسعد بخدمتكم\nشكراً لزيارتكم'
                    }
                }
            elif self.path == '/api/printers':
                data = {
                    'printers': [
                        {'name': 'طابعة حرارية 58مم - USB', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة حرارية 80مم - USB', 'is_default': True, 'status': 'online'},
                        {'name': 'Microsoft Print to PDF', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة الشبكة HP LaserJet', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة Canon', 'is_default': False, 'status': 'offline'}
                    ]
                }
            else:
                data = {'success': True, 'message': 'تم تنفيذ العملية بنجاح'}
            
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            return
        
        return super().do_GET()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
                action = data.get('action', 'unknown')
                payload = data.get('data', {})
                
                # طباعة تفاصيل العملية
                if action == 'print_invoice':
                    print(f"🖨️ طباعة فاتورة نظيفة:")
                    print(f"   • رقم الفاتورة: {payload.get('number', 'غير محدد')}")
                    print(f"   • المبلغ الإجمالي: {payload.get('total', 0)} ريال")
                    print(f"   • عدد الأصناف: {len(payload.get('items', []))}")
                    print(f"   • تصميم مبسط بدون معلومات إضافية")
                    
                elif action == 'save_design':
                    print(f"🎨 حفظ تصميم الفاتورة النظيفة:")
                    print(f"   • التركيز على المحتوى الأساسي فقط")
                    print(f"   • إخفاء المعلومات الإضافية")
                    
                else:
                    print(f"📝 إجراء: {action}")
                    
            except Exception as e:
                print(f"❌ خطأ في معالجة البيانات: {e}")
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = {'success': True, 'message': 'تم تنفيذ العملية بنجاح'}
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🎮 نظام نقاط البيع - الفاتورة النظيفة 🎮                  ║
    ║         POS System - Clean Invoice Design                   ║
    ║                                                              ║
    ║    ✨ الإصدار الأول - النسخة الأولى من برنامج الفواتير ✨     ║
    ║                                                              ║
    ║    👨‍💻 فكرة وتنفيذ: المهندس سيف رافع                        ║
    ║    💝 هذا البرنامج مجاني لوجه الله تعالى                     ║
    ║                                                              ║
    ║    📅 التاريخ: {date}                                        ║
    ║    ⏰ الوقت: {time}                                          ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """.format(
        date=datetime.now().strftime("%Y-%m-%d"),
        time=datetime.now().strftime("%H:%M:%S")
    )
    print(banner)

def start_server(port=8080):
    """بدء الخادم"""
    try:
        server = HTTPServer(('localhost', port), CleanInvoiceHandler)
        print(f"🌐 الخادم يعمل على: http://localhost:{port}")
        
        # فتح المتصفح بعد ثانيتين
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print("⚠️ لم يتم فتح المتصفح تلقائياً")
                print(f"   يرجى فتح الرابط يدوياً: http://localhost:{port}")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n" + "="*80)
        print("🎮 نظام الفاتورة النظيفة جاهز للاختبار!")
        print("="*80)
        print("🆕 التحديث الجديد:")
        print("   • ❌ تم إخفاء معلومات الكاشير")
        print("   • ❌ تم إخفاء عدد الأصناف")
        print("   • ❌ تم إزالة الجدول الإضافي")
        print("   • ✅ الاحتفاظ بالتاريخ والوقت فقط")
        print("   • ✅ تصميم أكثر نظافة وبساطة")
        print("   • ✅ التركيز على المحتوى الأساسي")
        print("="*80)
        print("📋 محتويات الفاتورة الآن:")
        print("   1. شعار ومعلومات المحل")
        print("   2. التاريخ والوقت فقط")
        print("   3. جدول الأصناف والأسعار")
        print("   4. المجموع الكلي")
        print("   5. رسالة الختام")
        print("="*80)
        print("🧪 اختبر الميزات:")
        print("   • أضف بعض الأصناف للفاتورة")
        print("   • استخدم 'معاينة الفاتورة'")
        print("   • لاحظ التصميم النظيف والمبسط")
        print("   • تحقق من عدم وجود معلومات إضافية")
        print("   • جرب الطباعة مع أحجام ورق مختلفة")
        print("="*80)
        print("💡 مميزات التصميم النظيف:")
        print("   • أسرع في الطباعة")
        print("   • أقل استهلاكاً للحبر")
        print("   • أكثر وضوحاً للعملاء")
        print("   • يركز على المعلومات المهمة فقط")
        print("="*80)
        print("⚠️ لإيقاف الخادم: اضغط Ctrl+C")
        print("="*80)
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 إيقاف الخادم...")
        server.shutdown()
        server.server_close()
        print("✅ تم إيقاف الخادم بنجاح")
        print("\n🙏 شكراً لاختبار الفاتورة النظيفة")
        print("💝 نسأل الله أن ينفع به ويجعله في ميزان حسناتنا")
        print("👨‍💻 فكرة وتنفيذ: المهندس سيف رافع")
        
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print("🔍 فحص الملفات المحدثة...")
    
    required_files = [
        'web_interface.html',
        'app.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع الملفات المطلوبة متوفرة")
    print("🚀 بدء اختبار الفاتورة النظيفة...")
    
    try:
        start_server()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
