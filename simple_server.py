#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler

class POSHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/web_interface.html'
        elif self.path.startswith('/api/'):
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            if self.path == '/api/data':
                data = {
                    'items': [
                        {'id': 1, 'name': 'قهوة عربية', 'price': 8.0, 'category': 'مشروبات'},
                        {'id': 2, 'name': 'شاي أحمر', 'price': 5.0, 'category': 'مشروبات'},
                        {'id': 3, 'name': 'ساعة بلايستيشن', 'price': 15.0, 'category': 'ألعاب'}
                    ],
                    'stats': {'today_invoices': 5, 'today_sales': 125.50, 'total_items': 3, 'avg_invoice': 25.10}
                }
            else:
                data = {'success': True}
            
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            return
        
        return super().do_GET()
    
    def do_POST(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'success': True}, ensure_ascii=False).encode('utf-8'))

def main():
    port = 8080
    print(f"🎮 نظام نقاط البيع يعمل على: http://localhost:{port}")
    
    server = HTTPServer(('localhost', port), POSHandler)
    webbrowser.open(f'http://localhost:{port}')
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
        server.shutdown()

if __name__ == "__main__":
    main()
