#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير البيانات الأساسية - حفظ واستعادة الإعدادات
Data Manager - Save and restore settings
"""

import json
import os
import sqlite3
from datetime import datetime
import shutil

class DataManager:
    def __init__(self, database):
        self.db = database
        self.config_file = "pos_config.json"
        self.backup_folder = "backups"
        self.ensure_backup_folder()
        
    def ensure_backup_folder(self):
        """التأكد من وجود مجلد النسخ الاحتياطية"""
        if not os.path.exists(self.backup_folder):
            os.makedirs(self.backup_folder)
    
    def save_basic_data(self, data):
        """حفظ البيانات الأساسية"""
        try:
            config = {
                'shop_info': {
                    'name': data.get('shop_name', 'مقهى البلايستيشن'),
                    'address': data.get('shop_address', 'الرياض - المملكة العربية السعودية'),
                    'phone': data.get('shop_phone', ''),
                    'email': data.get('shop_email', ''),
                    'logo_path': data.get('logo_path', ''),
                    'footer_message': data.get('footer_message', 'نسعد بخدمتكم')
                },
                'print_settings': {
                    'default_receipt_type': data.get('default_receipt_type', 'thermal_80'),
                    'default_printer': data.get('default_printer', ''),
                    'auto_print': data.get('auto_print', False),
                    'print_copies': data.get('print_copies', 1)
                },
                'ui_settings': {
                    'theme': data.get('theme', 'dark'),
                    'language': data.get('language', 'ar'),
                    'font_size': data.get('font_size', 12),
                    'window_maximized': data.get('window_maximized', True),
                    'sidebar_position': data.get('sidebar_position', 'right')
                },
                'business_settings': {
                    'tax_rate': data.get('tax_rate', 0.15),
                    'currency': data.get('currency', 'ريال'),
                    'invoice_prefix': data.get('invoice_prefix', 'INV'),
                    'auto_backup': data.get('auto_backup', True),
                    'backup_interval': data.get('backup_interval', 7)  # أيام
                },
                'last_updated': datetime.now().isoformat(),
                'version': '2.0'
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
            return False
    
    def load_basic_data(self):
        """تحميل البيانات الأساسية"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config
            else:
                # إنشاء إعدادات افتراضية
                default_config = self.create_default_config()
                self.save_basic_data(default_config)
                return default_config
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            return self.create_default_config()
    
    def create_default_config(self):
        """إنشاء إعدادات افتراضية"""
        return {
            'shop_name': 'مقهى البلايستيشن',
            'shop_address': 'الرياض - المملكة العربية السعودية',
            'shop_phone': '',
            'shop_email': '',
            'logo_path': '',
            'footer_message': 'نسعد بخدمتكم',
            'default_receipt_type': 'thermal_80',
            'default_printer': '',
            'auto_print': False,
            'print_copies': 1,
            'theme': 'dark',
            'language': 'ar',
            'font_size': 12,
            'window_maximized': True,
            'sidebar_position': 'right',
            'tax_rate': 0.15,
            'currency': 'ريال',
            'invoice_prefix': 'INV',
            'auto_backup': True,
            'backup_interval': 7
        }
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}"
            backup_path = os.path.join(self.backup_folder, backup_name)
            
            os.makedirs(backup_path, exist_ok=True)
            
            # نسخ قاعدة البيانات
            db_files = ['cafe_pos.db', 'basic_pos.db']
            for db_file in db_files:
                if os.path.exists(db_file):
                    shutil.copy2(db_file, backup_path)
            
            # نسخ ملف الإعدادات
            if os.path.exists(self.config_file):
                shutil.copy2(self.config_file, backup_path)
            
            # نسخ مجلد الشعارات
            if os.path.exists('logos'):
                shutil.copytree('logos', os.path.join(backup_path, 'logos'), dirs_exist_ok=True)
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                'created_at': datetime.now().isoformat(),
                'version': '2.0',
                'description': 'نسخة احتياطية تلقائية',
                'files_included': ['database', 'config', 'logos']
            }
            
            with open(os.path.join(backup_path, 'backup_info.json'), 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=4)
            
            return backup_path
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                return False
            
            # استعادة قاعدة البيانات
            db_files = ['cafe_pos.db', 'basic_pos.db']
            for db_file in db_files:
                backup_db = os.path.join(backup_path, db_file)
                if os.path.exists(backup_db):
                    shutil.copy2(backup_db, db_file)
            
            # استعادة ملف الإعدادات
            backup_config = os.path.join(backup_path, self.config_file)
            if os.path.exists(backup_config):
                shutil.copy2(backup_config, self.config_file)
            
            # استعادة مجلد الشعارات
            backup_logos = os.path.join(backup_path, 'logos')
            if os.path.exists(backup_logos):
                if os.path.exists('logos'):
                    shutil.rmtree('logos')
                shutil.copytree(backup_logos, 'logos')
            
            return True
            
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            backups = []
            if os.path.exists(self.backup_folder):
                for item in os.listdir(self.backup_folder):
                    backup_path = os.path.join(self.backup_folder, item)
                    if os.path.isdir(backup_path):
                        info_file = os.path.join(backup_path, 'backup_info.json')
                        if os.path.exists(info_file):
                            with open(info_file, 'r', encoding='utf-8') as f:
                                info = json.load(f)
                                backups.append({
                                    'name': item,
                                    'path': backup_path,
                                    'created_at': info.get('created_at', ''),
                                    'description': info.get('description', ''),
                                    'size': self.get_folder_size(backup_path)
                                })
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            return backups
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def get_folder_size(self, folder_path):
        """حساب حجم المجلد"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    total_size += os.path.getsize(filepath)
            
            # تحويل إلى وحدة مناسبة
            if total_size < 1024:
                return f"{total_size} بايت"
            elif total_size < 1024 * 1024:
                return f"{total_size / 1024:.1f} كيلوبايت"
            else:
                return f"{total_size / (1024 * 1024):.1f} ميجابايت"
                
        except:
            return "غير معروف"
    
    def auto_backup_check(self):
        """فحص الحاجة للنسخ الاحتياطي التلقائي"""
        try:
            config = self.load_basic_data()
            if not config.get('business_settings', {}).get('auto_backup', True):
                return False
            
            backup_interval = config.get('business_settings', {}).get('backup_interval', 7)
            
            # البحث عن آخر نسخة احتياطية
            backups = self.get_backup_list()
            if not backups:
                return True  # لا توجد نسخ احتياطية، يجب إنشاء واحدة
            
            last_backup = backups[0]
            last_backup_date = datetime.fromisoformat(last_backup['created_at'])
            days_since_backup = (datetime.now() - last_backup_date).days
            
            return days_since_backup >= backup_interval
            
        except Exception as e:
            print(f"خطأ في فحص النسخ الاحتياطي التلقائي: {e}")
            return False
    
    def export_data(self, export_path):
        """تصدير البيانات"""
        try:
            export_data = {
                'config': self.load_basic_data(),
                'items': self.export_items(),
                'invoices': self.export_invoices(),
                'export_date': datetime.now().isoformat(),
                'version': '2.0'
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=4)
            
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير البيانات: {e}")
            return False
    
    def export_items(self):
        """تصدير الأصناف"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM items')
            items = cursor.fetchall()
            conn.close()
            
            items_list = []
            for item in items:
                items_list.append({
                    'id': item[0],
                    'name': item[1],
                    'price': item[2],
                    'category': item[3] if len(item) > 3 else 'عام'
                })
            
            return items_list
            
        except Exception as e:
            print(f"خطأ في تصدير الأصناف: {e}")
            return []
    
    def export_invoices(self):
        """تصدير الفواتير"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM invoices ORDER BY created_at DESC LIMIT 100')
            invoices = cursor.fetchall()
            conn.close()
            
            invoices_list = []
            for invoice in invoices:
                invoices_list.append({
                    'id': invoice[0],
                    'invoice_number': invoice[1],
                    'total_amount': invoice[2],
                    'created_at': invoice[3] if len(invoice) > 3 else ''
                })
            
            return invoices_list
            
        except Exception as e:
            print(f"خطأ في تصدير الفواتير: {e}")
            return []
    
    def import_data(self, import_path):
        """استيراد البيانات"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # استيراد الإعدادات
            if 'config' in data:
                self.save_basic_data(data['config'])
            
            # استيراد الأصناف
            if 'items' in data:
                self.import_items(data['items'])
            
            return True
            
        except Exception as e:
            print(f"خطأ في استيراد البيانات: {e}")
            return False
    
    def import_items(self, items_data):
        """استيراد الأصناف"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            for item in items_data:
                cursor.execute('''INSERT OR REPLACE INTO items (name, price, category) 
                                 VALUES (?, ?, ?)''',
                              (item['name'], item['price'], item.get('category', 'عام')))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"خطأ في استيراد الأصناف: {e}")
            return False
