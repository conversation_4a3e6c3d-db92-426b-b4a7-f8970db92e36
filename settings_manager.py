import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from PIL import Image, ImageTk

class SettingsManager:
    def __init__(self, database):
        self.db = database
        self.logo_path = ""
        
    def open_settings_window(self, parent):
        """فتح نافذة الإعدادات"""
        # مسح المحتوى السابق
        for widget in parent.winfo_children():
            widget.destroy()
            
        # إنشاء الإطار الرئيسي
        main_frame = tk.Frame(parent, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(main_frame, text="⚙️ إعدادات المحل",
                              bg='#1a1a2e', fg='white',
                              font=('Arial', 20, 'bold'))
        title_label.pack(pady=(0, 30))
        
        # إطار المحتوى
        content_frame = tk.Frame(main_frame, bg='#16213e')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # تحميل الإعدادات الحالية
        settings = self.db.get_settings()
        
        # إنشاء نموذج الإعدادات
        self.create_settings_form(content_frame, settings)
    
    def create_settings_form(self, parent, settings):
        """إنشاء نموذج الإعدادات"""
        # إطار النموذج
        form_frame = tk.Frame(parent, bg='#16213e')
        form_frame.pack(fill='both', expand=True, padx=40, pady=40)
        
        # متغيرات النموذج
        self.shop_name_var = tk.StringVar()
        self.shop_address_var = tk.StringVar()
        self.footer_message_var = tk.StringVar()
        
        # ملء البيانات الحالية
        if settings:
            self.shop_name_var.set(settings[1] or "مقهى البلايستيشن")
            self.shop_address_var.set(settings[2] or "الرياض - المملكة العربية السعودية")
            self.logo_path = settings[3] or ""
            self.footer_message_var.set(settings[4] or "نسعد بخدمتكم")
        else:
            self.shop_name_var.set("مقهى البلايستيشن")
            self.shop_address_var.set("الرياض - المملكة العربية السعودية")
            self.footer_message_var.set("نسعد بخدمتكم")
        
        # الجانب الأيسر - معلومات المحل
        left_frame = tk.Frame(form_frame, bg='#16213e')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 20))
        
        # عنوان القسم
        tk.Label(left_frame, text="معلومات المحل",
                bg='#16213e', fg='white',
                font=('Arial', 16, 'bold')).pack(pady=(0, 20))
        
        # اسم المحل
        tk.Label(left_frame, text="اسم المحل:",
                bg='#16213e', fg='white',
                font=('Arial', 12, 'bold')).pack(anchor='w', pady=(10, 5))
        
        shop_name_entry = tk.Entry(left_frame, textvariable=self.shop_name_var,
                                  font=('Arial', 12), width=40)
        shop_name_entry.pack(fill='x', pady=(0, 15))
        
        # عنوان المحل
        tk.Label(left_frame, text="عنوان المحل:",
                bg='#16213e', fg='white',
                font=('Arial', 12, 'bold')).pack(anchor='w', pady=(10, 5))
        
        shop_address_entry = tk.Entry(left_frame, textvariable=self.shop_address_var,
                                     font=('Arial', 12), width=40)
        shop_address_entry.pack(fill='x', pady=(0, 15))
        
        # رسالة الختام
        tk.Label(left_frame, text="رسالة الختام:",
                bg='#16213e', fg='white',
                font=('Arial', 12, 'bold')).pack(anchor='w', pady=(10, 5))
        
        footer_entry = tk.Entry(left_frame, textvariable=self.footer_message_var,
                               font=('Arial', 12), width=40)
        footer_entry.pack(fill='x', pady=(0, 20))
        
        # الجانب الأيمن - الشعار
        right_frame = tk.Frame(form_frame, bg='#16213e')
        right_frame.pack(side='right', fill='both', expand=True, padx=(20, 0))
        
        # عنوان قسم الشعار
        tk.Label(right_frame, text="شعار المحل",
                bg='#16213e', fg='white',
                font=('Arial', 16, 'bold')).pack(pady=(0, 20))
        
        # إطار عرض الشعار
        logo_display_frame = tk.Frame(right_frame, bg='#0f3460', width=200, height=200)
        logo_display_frame.pack(pady=(0, 20))
        logo_display_frame.pack_propagate(False)
        
        # تسمية عرض الشعار
        self.logo_label = tk.Label(logo_display_frame, text="لا يوجد شعار",
                                  bg='#0f3460', fg='white',
                                  font=('Arial', 12))
        self.logo_label.pack(expand=True)
        
        # تحميل الشعار إذا كان موجوداً
        self.load_logo_preview()
        
        # أزرار الشعار
        logo_buttons_frame = tk.Frame(right_frame, bg='#16213e')
        logo_buttons_frame.pack(fill='x', pady=(0, 20))
        
        # زر اختيار الشعار
        choose_logo_btn = tk.Button(logo_buttons_frame, text="📁 اختيار شعار",
                                   command=self.choose_logo,
                                   bg='#3498db', fg='white',
                                   font=('Arial', 12, 'bold'),
                                   relief='flat', cursor='hand2',
                                   width=15, height=2)
        choose_logo_btn.pack(side='left', padx=(0, 10))
        
        # زر حذف الشعار
        remove_logo_btn = tk.Button(logo_buttons_frame, text="🗑️ حذف الشعار",
                                   command=self.remove_logo,
                                   bg='#e74c3c', fg='white',
                                   font=('Arial', 12, 'bold'),
                                   relief='flat', cursor='hand2',
                                   width=15, height=2)
        remove_logo_btn.pack(side='left')
        
        # إطار الأزرار السفلي
        buttons_frame = tk.Frame(parent, bg='#16213e')
        buttons_frame.pack(fill='x', pady=20)
        
        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="💾 حفظ الإعدادات",
                            command=self.save_settings,
                            bg='#27ae60', fg='white',
                            font=('Arial', 14, 'bold'),
                            relief='flat', cursor='hand2',
                            width=20, height=2)
        save_btn.pack(side='left', padx=20)
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                              command=self.cancel_settings,
                              bg='#95a5a6', fg='white',
                              font=('Arial', 14, 'bold'),
                              relief='flat', cursor='hand2',
                              width=15, height=2)
        cancel_btn.pack(side='left', padx=10)
        
        # زر استعادة الافتراضي
        reset_btn = tk.Button(buttons_frame, text="🔄 استعادة الافتراضي",
                             command=self.reset_to_default,
                             bg='#f39c12', fg='white',
                             font=('Arial', 14, 'bold'),
                             relief='flat', cursor='hand2',
                             width=20, height=2)
        reset_btn.pack(side='right', padx=20)
        
        # زر معاينة الفاتورة
        preview_btn = tk.Button(buttons_frame, text="👁️ معاينة الفاتورة",
                               command=self.preview_invoice,
                               bg='#9b59b6', fg='white',
                               font=('Arial', 14, 'bold'),
                               relief='flat', cursor='hand2',
                               width=20, height=2)
        preview_btn.pack(side='right', padx=10)
    
    def choose_logo(self):
        """اختيار ملف الشعار"""
        file_types = [
            ('Image files', '*.png *.jpg *.jpeg *.gif *.bmp'),
            ('PNG files', '*.png'),
            ('JPEG files', '*.jpg *.jpeg'),
            ('All files', '*.*')
        ]
        
        filename = filedialog.askopenfilename(
            title="اختيار شعار المحل",
            filetypes=file_types
        )
        
        if filename:
            try:
                # التحقق من صحة الصورة
                img = Image.open(filename)
                img.verify()
                
                # نسخ الملف إلى مجلد البرنامج
                logo_dir = "logos"
                if not os.path.exists(logo_dir):
                    os.makedirs(logo_dir)
                
                # إنشاء اسم ملف جديد
                file_extension = os.path.splitext(filename)[1]
                new_filename = f"shop_logo{file_extension}"
                new_path = os.path.join(logo_dir, new_filename)
                
                # نسخ الملف
                img = Image.open(filename)
                # تغيير حجم الصورة إذا كانت كبيرة
                img.thumbnail((200, 200), Image.Resampling.LANCZOS)
                img.save(new_path)
                
                self.logo_path = new_path
                self.load_logo_preview()
                
                messagebox.showinfo("نجح", "تم تحميل الشعار بنجاح")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في تحميل الشعار: {str(e)}")
    
    def remove_logo(self):
        """حذف الشعار"""
        if messagebox.askyesno("تأكيد", "هل تريد حذف الشعار؟"):
            self.logo_path = ""
            self.logo_label.config(image="", text="لا يوجد شعار")
            messagebox.showinfo("تم", "تم حذف الشعار")
    
    def load_logo_preview(self):
        """تحميل معاينة الشعار"""
        if self.logo_path and os.path.exists(self.logo_path):
            try:
                # تحميل وتغيير حجم الصورة
                img = Image.open(self.logo_path)
                img.thumbnail((180, 180), Image.Resampling.LANCZOS)
                
                # تحويل إلى PhotoImage
                photo = ImageTk.PhotoImage(img)
                
                # عرض الصورة
                self.logo_label.config(image=photo, text="")
                self.logo_label.image = photo  # الاحتفاظ بمرجع
                
            except Exception as e:
                self.logo_label.config(image="", text="خطأ في تحميل الشعار")
        else:
            self.logo_label.config(image="", text="لا يوجد شعار")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        shop_name = self.shop_name_var.get().strip()
        shop_address = self.shop_address_var.get().strip()
        footer_message = self.footer_message_var.get().strip()
        
        # التحقق من صحة البيانات
        if not shop_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المحل")
            return
        
        if not shop_address:
            messagebox.showerror("خطأ", "يرجى إدخال عنوان المحل")
            return
        
        if not footer_message:
            messagebox.showerror("خطأ", "يرجى إدخال رسالة الختام")
            return
        
        try:
            # حفظ الإعدادات في قاعدة البيانات
            self.db.update_settings(shop_name, shop_address, self.logo_path, footer_message)
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
    
    def cancel_settings(self):
        """إلغاء التغييرات"""
        if messagebox.askyesno("تأكيد", "هل تريد إلغاء التغييرات؟"):
            # إعادة تحميل الإعدادات
            settings = self.db.get_settings()
            if settings:
                self.shop_name_var.set(settings[1] or "مقهى البلايستيشن")
                self.shop_address_var.set(settings[2] or "الرياض - المملكة العربية السعودية")
                self.logo_path = settings[3] or ""
                self.footer_message_var.set(settings[4] or "نسعد بخدمتكم")
                self.load_logo_preview()
    
    def reset_to_default(self):
        """استعادة الإعدادات الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد استعادة الإعدادات الافتراضية؟"):
            self.shop_name_var.set("مقهى البلايستيشن")
            self.shop_address_var.set("الرياض - المملكة العربية السعودية")
            self.footer_message_var.set("نسعد بخدمتكم")
            self.logo_path = ""
            self.load_logo_preview()
    
    def preview_invoice(self):
        """معاينة الفاتورة بالإعدادات الحالية"""
        try:
            # إنشاء فاتورة تجريبية
            from thermal_printer import ThermalPrinter
            
            # حفظ الإعدادات مؤقتاً
            temp_settings = (
                1,
                self.shop_name_var.get(),
                self.shop_address_var.get(),
                self.logo_path,
                self.footer_message_var.get()
            )
            
            # إنشاء أصناف تجريبية
            sample_items = [
                {'name': 'ساعة بلايستيشن', 'price': 15.0, 'quantity': 2, 'subtotal': 30.0},
                {'name': 'قهوة', 'price': 8.0, 'quantity': 1, 'subtotal': 8.0},
                {'name': 'ساندويش', 'price': 20.0, 'quantity': 1, 'subtotal': 20.0}
            ]
            
            total_amount = 58.0
            
            # إنشاء طابعة حرارية مؤقتة
            class TempDB:
                def get_settings(self):
                    return temp_settings
            
            temp_printer = ThermalPrinter(TempDB())
            
            # إنشاء الفاتورة
            pdf_path = temp_printer.create_thermal_receipt_pdf(
                "PREVIEW-001", sample_items, total_amount
            )
            
            # فتح الفاتورة للمعاينة
            temp_printer.open_for_printing(pdf_path)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء المعاينة: {str(e)}")
    
    def export_settings(self):
        """تصدير الإعدادات"""
        try:
            settings = self.db.get_settings()
            if not settings:
                messagebox.showwarning("تحذير", "لا توجد إعدادات للتصدير")
                return
            
            filename = filedialog.asksaveasfilename(
                title="تصدير الإعدادات",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"اسم المحل: {settings[1]}\n")
                    f.write(f"عنوان المحل: {settings[2]}\n")
                    f.write(f"مسار الشعار: {settings[3]}\n")
                    f.write(f"رسالة الختام: {settings[4]}\n")
                    f.write(f"تاريخ التحديث: {settings[5]}\n")
                
                messagebox.showinfo("نجح", "تم تصدير الإعدادات بنجاح")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الإعدادات: {str(e)}")
    
    def import_settings(self):
        """استيراد الإعدادات"""
        try:
            filename = filedialog.askopenfilename(
                title="استيراد الإعدادات",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            
            if filename:
                # قراءة الملف وتحليل الإعدادات
                # هذه وظيفة متقدمة يمكن تطويرها لاحقاً
                messagebox.showinfo("معلومات", "وظيفة الاستيراد قيد التطوير")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في استيراد الإعدادات: {str(e)}")
