// نظام نقاط البيع - JavaScript
// PlayStation Cafe POS System - JavaScript

// متغيرات عامة
let currentInvoice = {
    number: '',
    items: [],
    total: 0
};

let selectedItem = null;
let items = [];
let invoices = [];
let settings = {};

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadData();
    setupEventListeners();
    updateTime();
    setInterval(updateTime, 1000);
});

// تهيئة التطبيق
function initializeApp() {
    generateInvoiceNumber();
    loadSettings();
    updateStats();
    
    // تحميل البيانات من الخادم
    fetchData();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // التنقل في القائمة
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page');
            showPage(page);
            
            // تحديث القائمة النشطة
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // البحث في الأصناف
    document.getElementById('itemSearch').addEventListener('input', function() {
        filterItems(this.value);
    });

    // زر القائمة للجوال
    document.getElementById('menuToggle').addEventListener('click', function() {
        document.getElementById('sidebar').classList.toggle('open');
    });

    // إغلاق القائمة عند النقر خارجها
    document.addEventListener('click', function(e) {
        const sidebar = document.getElementById('sidebar');
        const menuToggle = document.getElementById('menuToggle');
        
        if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
            sidebar.classList.remove('open');
        }
    });
}

// عرض الصفحة
function showPage(pageId) {
    // إخفاء جميع الصفحات
    document.querySelectorAll('.page-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // عرض الصفحة المطلوبة
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.add('active');
        
        // تحديث عنوان الصفحة
        const titles = {
            'dashboard': 'لوحة التحكم',
            'invoice': 'إصدار فاتورة',
            'items': 'إدارة الأصناف',
            'reports': 'التقارير',
            'settings': 'الإعدادات',
            'invoice-design': 'تصميم الفاتورة',
            'printers': 'إعدادات الطباعة'
        };
        
        document.getElementById('pageTitle').textContent = titles[pageId] || 'نظام نقاط البيع';
        
        // تحميل محتوى الصفحة
        loadPageContent(pageId);
    }
}

// تحميل محتوى الصفحة
function loadPageContent(pageId) {
    switch(pageId) {
        case 'dashboard':
            updateStats();
            break;
        case 'invoice':
            loadItems();
            break;
        case 'items':
            loadItemsManagement();
            break;
        case 'reports':
            loadReports();
            break;
        case 'settings':
            loadSettings();
            break;
        case 'invoice-design':
            loadInvoiceDesign();
            break;
        case 'printers':
            loadPrinterSettings();
            break;
    }
}

// تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// توليد رقم فاتورة
function generateInvoiceNumber() {
    const now = new Date();
    const timestamp = now.getFullYear().toString() + 
                     (now.getMonth() + 1).toString().padStart(2, '0') + 
                     now.getDate().toString().padStart(2, '0') + 
                     now.getHours().toString().padStart(2, '0') + 
                     now.getMinutes().toString().padStart(2, '0');
    
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    currentInvoice.number = `INV-${timestamp}-${random}`;
    
    const invoiceNumberElement = document.getElementById('invoiceNumber');
    if (invoiceNumberElement) {
        invoiceNumberElement.value = currentInvoice.number;
    }
}

// تحميل الأصناف
function loadItems() {
    // بيانات تجريبية
    items = [
        { id: 1, name: 'ساعة بلايستيشن', price: 15.00, category: 'ألعاب' },
        { id: 2, name: 'قهوة عربية', price: 8.00, category: 'مشروبات' },
        { id: 3, name: 'شاي أحمر', price: 5.00, category: 'مشروبات' },
        { id: 4, name: 'عصير برتقال', price: 10.00, category: 'مشروبات' },
        { id: 5, name: 'ساندويش تونة', price: 20.00, category: 'طعام' },
        { id: 6, name: 'بيتزا صغيرة', price: 35.00, category: 'طعام' },
        { id: 7, name: 'مياه معدنية', price: 2.00, category: 'مشروبات' },
        { id: 8, name: 'كولا', price: 6.00, category: 'مشروبات' }
    ];
    
    displayItems(items);
}

// عرض الأصناف مع دعم النقر المزدوج
function displayItems(itemsToShow) {
    const itemsList = document.getElementById('itemsList');
    if (!itemsList) return;

    itemsList.innerHTML = '';

    itemsToShow.forEach(item => {
        const itemCard = document.createElement('div');
        itemCard.className = 'item-card';

        // النقر العادي للاختيار
        itemCard.onclick = () => selectItem(item, itemCard);

        // النقر المزدوج للإضافة المباشرة
        itemCard.ondblclick = (e) => {
            e.preventDefault();
            e.stopPropagation();

            // إضافة الصنف مباشرة للفاتورة
            addItemToInvoice(item);

            // تأثير بصري للنقر المزدوج
            itemCard.style.transform = 'scale(0.95)';
            itemCard.style.background = 'linear-gradient(135deg, #00d4aa, #1a1a2e)';
            itemCard.style.color = 'white';
            itemCard.style.transition = 'all 0.2s ease';

            // إظهار رسالة تأكيد
            showNotification(`تم إضافة "${item.name}" للفاتورة`, 'success');

            setTimeout(() => {
                itemCard.style.transform = '';
                itemCard.style.background = '';
                itemCard.style.color = '';
            }, 300);
        };

        // إضافة مؤشر بصري
        itemCard.style.cursor = 'pointer';
        itemCard.title = 'انقر مرة للاختيار، انقر مرتين للإضافة المباشرة';

        itemCard.innerHTML = `
            <div class="item-info">
                <div class="item-name">${item.name}</div>
                <div class="item-details">${item.category}</div>
                <small style="color: #666; font-size: 10px; margin-top: 3px; display: block;">
                    💡 انقر مرتين للإضافة السريعة
                </small>
            </div>
            <div class="item-price">${item.price.toFixed(2)} ريال</div>
        `;

        itemsList.appendChild(itemCard);
    });
}

// تصفية الأصناف
function filterItems(searchTerm) {
    const filteredItems = items.filter(item => 
        item.name.includes(searchTerm) || 
        item.category.includes(searchTerm)
    );
    displayItems(filteredItems);
}

// اختيار صنف
function selectItem(item, cardElement = null) {
    selectedItem = item;

    // تمييز الصنف المختار
    document.querySelectorAll('.item-card').forEach(card => {
        card.classList.remove('selected');
    });

    // إذا لم يتم تمرير العنصر، ابحث عنه
    if (!cardElement) {
        const cards = document.querySelectorAll('.item-card');
        cards.forEach(card => {
            if (card.querySelector('.item-name').textContent === item.name) {
                cardElement = card;
            }
        });
    }

    if (cardElement) {
        cardElement.classList.add('selected');
    }
}

// تغيير الكمية
function changeQuantity(change) {
    const quantityInput = document.getElementById('quantity');
    if (!quantityInput) return;
    
    let currentQuantity = parseInt(quantityInput.value) || 1;
    currentQuantity += change;
    
    if (currentQuantity < 1) currentQuantity = 1;
    
    quantityInput.value = currentQuantity;
}

// إضافة للفاتورة
function addToInvoice() {
    if (!selectedItem) {
        showNotification('يرجى اختيار صنف أولاً', 'warning');
        return;
    }
    
    const quantity = parseInt(document.getElementById('quantity').value) || 1;
    const subtotal = selectedItem.price * quantity;
    
    // البحث عن الصنف في الفاتورة
    const existingItemIndex = currentInvoice.items.findIndex(item => item.id === selectedItem.id);
    
    if (existingItemIndex > -1) {
        // تحديث الكمية
        currentInvoice.items[existingItemIndex].quantity += quantity;
        currentInvoice.items[existingItemIndex].subtotal = 
            currentInvoice.items[existingItemIndex].price * currentInvoice.items[existingItemIndex].quantity;
    } else {
        // إضافة صنف جديد
        currentInvoice.items.push({
            id: selectedItem.id,
            name: selectedItem.name,
            price: selectedItem.price,
            quantity: quantity,
            subtotal: subtotal
        });
    }
    
    updateInvoiceDisplay();
    showNotification(`تم إضافة ${selectedItem.name} للفاتورة`, 'success');
    
    // إعادة تعيين الكمية
    document.getElementById('quantity').value = 1;
}

// إضافة صنف للفاتورة مباشرة (للنقر المزدوج)
function addItemToInvoice(item, quantity = 1) {
    if (!item) return;

    const subtotal = item.price * quantity;

    // البحث عن الصنف في الفاتورة
    const existingItemIndex = currentInvoice.items.findIndex(invoiceItem => invoiceItem.id === item.id);

    if (existingItemIndex > -1) {
        // تحديث الكمية
        currentInvoice.items[existingItemIndex].quantity += quantity;
        currentInvoice.items[existingItemIndex].subtotal =
            currentInvoice.items[existingItemIndex].price * currentInvoice.items[existingItemIndex].quantity;
    } else {
        // إضافة صنف جديد
        currentInvoice.items.push({
            id: item.id,
            name: item.name,
            price: item.price,
            quantity: quantity,
            subtotal: subtotal
        });
    }

    updateInvoiceDisplay();
}

// تحديث عرض الفاتورة
function updateInvoiceDisplay() {
    const invoiceItems = document.getElementById('invoiceItems');
    if (!invoiceItems) return;
    
    invoiceItems.innerHTML = '';
    currentInvoice.total = 0;
    
    currentInvoice.items.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.name}</td>
            <td>${item.price.toFixed(2)} ريال</td>
            <td>${item.quantity}</td>
            <td>${item.subtotal.toFixed(2)} ريال</td>
            <td>
                <button class="btn btn-danger btn-sm" onclick="removeFromInvoice(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        invoiceItems.appendChild(row);
        currentInvoice.total += item.subtotal;
    });
    
    // تحديث المجموع
    const totalElement = document.getElementById('totalAmount');
    if (totalElement) {
        totalElement.textContent = `${currentInvoice.total.toFixed(2)} ريال`;
    }
}

// حذف من الفاتورة
function removeFromInvoice(index) {
    if (confirm('هل تريد حذف هذا الصنف من الفاتورة؟')) {
        currentInvoice.items.splice(index, 1);
        updateInvoiceDisplay();
        showNotification('تم حذف الصنف من الفاتورة', 'info');
    }
}

// حفظ الفاتورة
function saveInvoice() {
    if (currentInvoice.items.length === 0) {
        showNotification('لا توجد أصناف في الفاتورة', 'warning');
        return;
    }
    
    // إضافة الفاتورة للقائمة
    invoices.push({
        ...currentInvoice,
        date: new Date().toISOString(),
        saved: true
    });
    
    // حفظ في التخزين المحلي
    localStorage.setItem('invoices', JSON.stringify(invoices));
    
    showNotification(`تم حفظ الفاتورة ${currentInvoice.number}`, 'success');
    
    // إرسال للخادم
    sendToServer('save_invoice', currentInvoice);
}

// طباعة الفاتورة
function printInvoice() {
    if (currentInvoice.items.length === 0) {
        showNotification('لا توجد أصناف في الفاتورة', 'warning');
        return;
    }

    // عرض معاينة الفاتورة قبل الطباعة
    showPrintPreview();
}

// عرض معاينة الطباعة
function showPrintPreview() {
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'printPreviewModal';

    const now = new Date();
    const dateStr = now.toLocaleDateString('ar-SA');
    const timeStr = now.toLocaleTimeString('ar-SA');

    // الحصول على إعدادات التصميم لتطبيقها على الجدول
    const designSettings = JSON.parse(localStorage.getItem('invoiceDesign') || '{}');
    const contentFontSize = designSettings.contentFontSize || 14;
    const textColor = designSettings.textColor || '#000000';

    let itemsHtml = '';
    currentInvoice.items.forEach(item => {
        itemsHtml += `
            <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 8px 6px; text-align: center; font-weight: 600; color: ${textColor}; font-size: ${Math.max(contentFontSize - 1, 11)}px;">${item.name}</td>
                <td style="padding: 8px 6px; text-align: center; color: #00d4aa; font-weight: 600; font-size: ${Math.max(contentFontSize - 1, 11)}px;">${item.price.toFixed(2)} ريال</td>
                <td style="padding: 8px 6px; text-align: center; background: #f8f9fa; font-weight: 600; font-size: ${Math.max(contentFontSize - 1, 11)}px;">${item.quantity}</td>
                <td style="padding: 8px 6px; text-align: center; color: ${textColor}; font-weight: 700; font-size: ${Math.max(contentFontSize, 12)}px;">${item.subtotal.toFixed(2)} ريال</td>
            </tr>
        `;
    });

    modal.innerHTML = `
        <div class="modal-content" style="max-width: 800px; max-height: 90vh;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #f0f0f0;">
                <h2 style="margin: 0; color: var(--primary);">
                    <i class="fas fa-eye"></i>
                    معاينة الفاتورة
                </h2>
                <button onclick="closePrintPreview()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #999;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="invoicePreviewContent" style="border: 2px solid #ddd; border-radius: 10px; padding: 20px; background: white; max-height: 60vh; overflow-y: auto;">
                ${generateInvoiceHTML(itemsHtml, dateStr, timeStr)}
            </div>

            <div style="display: flex; gap: 15px; margin-top: 20px; justify-content: center;">
                <button class="btn btn-success" onclick="confirmPrint()">
                    <i class="fas fa-print"></i>
                    طباعة الفاتورة
                </button>
                <button class="btn btn-info" onclick="downloadInvoice()">
                    <i class="fas fa-download"></i>
                    تحميل PDF
                </button>
                <button class="btn btn-secondary" onclick="closePrintPreview()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// إنشاء HTML الفاتورة مع تطبيق إعدادات التصميم
function generateInvoiceHTML(itemsHtml, dateStr, timeStr) {
    // الحصول على إعدادات التصميم المحفوظة
    const designSettings = JSON.parse(localStorage.getItem('invoiceDesign') || '{}');
    const printerSettings = JSON.parse(localStorage.getItem('printerSettings') || '{}');

    // تطبيق إعدادات التصميم
    const paperType = designSettings.paperType || printerSettings.defaultReceiptType || 'thermal_80';
    const bgColor = designSettings.bgColor || '#ffffff';
    const textColor = designSettings.textColor || '#000000';
    const headerColor = designSettings.headerColor || '#1a1a2e';
    const titleFontSize = designSettings.titleFontSize || 24;
    const contentFontSize = designSettings.contentFontSize || 14;
    const lineHeight = designSettings.lineHeight || 1.5;
    const invoiceWidth = getPaperWidth(paperType);

    // معلومات المحل من إعدادات التصميم أو الإعدادات العامة
    const shopName = designSettings.shopName || settings.shopName || 'مقهى البلايستيشن';
    const shopAddress = designSettings.shopAddress || settings.shopAddress || 'الرياض - المملكة العربية السعودية';
    const shopPhone = designSettings.shopPhone || settings.shopPhone || '+966 11 234 5678';
    const shopEmail = designSettings.shopEmail || settings.shopEmail || '';
    const welcomeMsg = designSettings.welcomeMsg || 'أهلاً وسهلاً بكم';
    const footerMsg = designSettings.footerMsg || settings.footerMessage || 'نسعد بخدمتكم';

    // تحويل التاريخ والوقت للتنسيق العربي
    const arabicDateStr = dateStr || new Date().toLocaleDateString('ar-SA');
    const arabicTimeStr = timeStr || new Date().toLocaleTimeString('ar-SA');

    return `
        <div style="
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: center;
            color: ${textColor};
            background: ${bgColor};
            width: ${invoiceWidth}px;
            margin: 0 auto;
            font-size: ${contentFontSize}px;
            line-height: ${lineHeight};
            padding: 0;
        ">
            <div style="background: linear-gradient(135deg, ${headerColor}, #16213e); color: white; padding: 20px; position: relative; overflow: hidden;">
                <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent); animation: shimmer 3s infinite;"></div>
                <div style="position: relative; z-index: 1;">
                    ${getInvoiceHeaderHTML({shopName, shopAddress, shopPhone, shopEmail})}
                    <p style="margin: 0; opacity: 0.9; font-size: ${Math.max(contentFontSize - 2, 10)}px;">${shopAddress}</p>
                    <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: ${Math.max(contentFontSize - 2, 10)}px;">${shopPhone}</p>
                    ${shopEmail ? `<p style="margin: 5px 0 0 0; opacity: 0.9; font-size: ${Math.max(contentFontSize - 2, 10)}px;">${shopEmail}</p>` : ''}

                    <!-- رقم الفاتورة -->
                    <div style="margin-top: 15px; padding: 8px; background: rgba(0, 212, 170, 0.2); border-radius: 6px; border: 1px solid rgba(0, 212, 170, 0.4); text-align: center;">
                        <div style="font-size: ${Math.max(contentFontSize - 1, 11)}px; font-weight: 600;">
                            🧾 فاتورة رقم: ${currentInvoice.number}
                        </div>
                        <div style="font-size: ${Math.max(contentFontSize - 3, 9)}px; opacity: 0.8; margin-top: 2px;">
                            ${arabicDateStr} - ${arabicTimeStr}
                        </div>
                    </div>
                </div>
            </div>

            <div style="background: #f8f9fa; padding: 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: right;">
                <div style="background: white; padding: 15px; border-radius: 10px; border-right: 4px solid #667eea;">
                    <h3 style="margin: 0 0 10px 0; color: #1a1a2e; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-receipt"></i>
                        معلومات الفاتورة
                    </h3>
                    <p style="margin: 5px 0;"><strong>رقم الفاتورة:</strong> ${currentInvoice.number}</p>
                    <p style="margin: 5px 0;"><strong>التاريخ:</strong> ${dateStr}</p>
                    <p style="margin: 5px 0;"><strong>الوقت:</strong> ${timeStr}</p>
                </div>

                <div style="background: white; padding: 15px; border-radius: 10px; border-right: 4px solid #00d4aa;">
                    <h3 style="margin: 0 0 10px 0; color: #1a1a2e; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-store"></i>
                        معلومات إضافية
                    </h3>
                    <p style="margin: 5px 0;"><strong>الكاشير:</strong> النظام</p>
                    <p style="margin: 5px 0;"><strong>نوع الدفع:</strong> نقدي</p>
                    <p style="margin: 5px 0;"><strong>عدد الأصناف:</strong> ${currentInvoice.items.length}</p>
                </div>
            </div>

            <div style="padding: 10px; background: ${bgColor};">
                <div style="margin: 10px 0;">
                    <div style="background: rgba(0, 212, 170, 0.1); padding: 8px; border-radius: 6px; margin-bottom: 10px; text-align: center;">
                        <h3 style="margin: 0; font-size: ${Math.max(contentFontSize + 2, 14)}px; color: ${headerColor};">🛍️ تفاصيل الطلب</h3>
                    </div>

                    <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; font-size: ${Math.max(contentFontSize - 1, 11)}px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <thead>
                            <tr style="background: ${headerColor}; color: white;">
                                <th style="padding: 8px 6px; text-align: center; font-weight: 600;">الصنف</th>
                                <th style="padding: 8px 6px; text-align: center; font-weight: 600;">السعر</th>
                                <th style="padding: 8px 6px; text-align: center; font-weight: 600;">الكمية</th>
                                <th style="padding: 8px 6px; text-align: center; font-weight: 600;">المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${itemsHtml}
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="background: linear-gradient(135deg, ${headerColor}, #16213e); color: white; padding: 15px; text-align: center; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent); animation: slide 2s infinite;"></div>
                <div style="position: relative; z-index: 1;">
                    <div style="font-size: ${Math.max(contentFontSize - 1, 11)}px; margin-bottom: 6px; opacity: 0.9;">المجموع الكلي</div>
                    <div style="font-size: ${Math.max(contentFontSize + 4, 16)}px; font-weight: 700; color: #00d4aa; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                        ${currentInvoice.total.toFixed(2)} ريال
                    </div>
                </div>
            </div>

            <div style="background: ${bgColor}; padding: 15px; text-align: center; border-top: 2px dashed #ddd;">
                <div style="background: rgba(0, 212, 170, 0.1); padding: 10px; border-radius: 8px; margin-bottom: 10px;">
                    <div style="font-size: ${Math.max(contentFontSize, 12)}px; color: ${textColor}; font-weight: 600; margin-bottom: 5px;">
                        ${footerMsg}
                    </div>
                    <div style="font-size: ${Math.max(contentFontSize - 2, 10)}px; color: #666; font-style: italic;">
                        شكراً لزيارتكم ${shopName}
                    </div>
                </div>

                <div style="font-size: ${Math.max(contentFontSize - 3, 9)}px; color: #999; margin-top: 8px;">
                    تم إنشاء هذه الفاتورة بواسطة نظام نقاط البيع المتقدم
                </div>
            </div>
        </div>

        <style>
            @keyframes shimmer {
                0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
                100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            }

            @keyframes slide {
                0% { left: -100%; }
                100% { left: 100%; }
            }

            tbody tr:nth-child(even) {
                background: #f8f9fa;
            }

            tbody tr:hover {
                background: #e3f2fd;
            }

            tbody td {
                padding: 12px 15px;
                text-align: center;
                border-bottom: 1px solid #eee;
            }
        </style>
    `;
}

// فاتورة جديدة
function newInvoice() {
    if (currentInvoice.items.length > 0) {
        if (!confirm('هل تريد إنشاء فاتورة جديدة؟ سيتم فقدان البيانات الحالية.')) {
            return;
        }
    }
    
    currentInvoice = {
        number: '',
        items: [],
        total: 0
    };
    
    generateInvoiceNumber();
    updateInvoiceDisplay();
    
    // إعادة تعيين الاختيارات
    selectedItem = null;
    document.querySelectorAll('.item-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    showNotification('تم إنشاء فاتورة جديدة', 'info');
}

// تحديث الإحصائيات
function updateStats() {
    // حساب إحصائيات اليوم
    const today = new Date().toDateString();
    const todayInvoices = invoices.filter(inv => 
        new Date(inv.date).toDateString() === today
    );
    
    const todaySales = todayInvoices.reduce((sum, inv) => sum + inv.total, 0);
    const avgInvoice = todayInvoices.length > 0 ? todaySales / todayInvoices.length : 0;
    
    // تحديث العناصر
    updateElement('todayInvoices', todayInvoices.length);
    updateElement('todaySales', todaySales.toFixed(2));
    updateElement('totalItems', items.length);
    updateElement('avgInvoice', avgInvoice.toFixed(2));
}

// تحديث عنصر
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

// عرض الإشعار
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    if (!notification) return;
    
    const colors = {
        success: '#00d4aa',
        warning: '#ffb347',
        danger: '#ff6b6b',
        info: '#4ecdc4'
    };
    
    notification.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            left: 20px;
            background: ${colors[type]};
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
        ">
            ${message}
        </div>
    `;
    
    setTimeout(() => {
        notification.innerHTML = '';
    }, 3000);
}

// تحميل البيانات
function loadData() {
    // تحميل من التخزين المحلي
    const savedInvoices = localStorage.getItem('invoices');
    if (savedInvoices) {
        invoices = JSON.parse(savedInvoices);
    }
    
    const savedSettings = localStorage.getItem('settings');
    if (savedSettings) {
        settings = JSON.parse(savedSettings);
    }
}

// جلب البيانات من الخادم
function fetchData() {
    // هنا يتم الاتصال بخادم Python
    fetch('/api/data')
        .then(response => response.json())
        .then(data => {
            if (data.items) items = data.items;
            if (data.invoices) invoices = data.invoices;
            if (data.settings) settings = data.settings;
            
            updateStats();
            displayItems(items);
        })
        .catch(error => {
            console.log('استخدام البيانات المحلية:', error);
        });
}

// إرسال للخادم
function sendToServer(action, data) {
    fetch('/api/action', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: action,
            data: data
        })
    })
    .then(response => response.json())
    .then(result => {
        console.log('تم الإرسال للخادم:', result);
    })
    .catch(error => {
        console.log('خطأ في الإرسال:', error);
    });
}

// تحميل الإعدادات
function loadSettings() {
    // إعدادات افتراضية
    settings = {
        shopName: 'مقهى البلايستيشن',
        shopAddress: 'الرياض - المملكة العربية السعودية',
        shopPhone: '+966 11 234 5678',
        shopEmail: '<EMAIL>',
        footerMessage: 'نسعد بخدمتكم',
        defaultReceiptType: 'thermal_80',
        defaultPrinter: '',
        autoPrint: false,
        ...settings
    };
}

// تحميل إدارة الأصناف
function loadItemsManagement() {
    // سيتم تطويرها في ملف منفصل
    console.log('تحميل إدارة الأصناف');
}

// تحميل التقارير
function loadReports() {
    // سيتم تطويرها في ملف منفصل
    console.log('تحميل التقارير');
}

// تحميل تصميم الفاتورة
function loadInvoiceDesign() {
    // سيتم تطويرها في ملف منفصل
    console.log('تحميل تصميم الفاتورة');
}

// تحميل إعدادات الطباعة
function loadPrinterSettings() {
    // سيتم تطويرها في ملف منفصل
    console.log('تحميل إعدادات الطباعة');
}

// إضافة أنماط CSS للتفاعل
const style = document.createElement('style');
style.textContent = `
    .item-card.selected {
        background: #e3f2fd !important;
        border-right: 4px solid var(--info) !important;
        transform: translateX(-5px) !important;
    }
    
    .btn-sm {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
    
    @keyframes slideIn {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
`;
document.head.appendChild(style);

// إغلاق معاينة الطباعة
function closePrintPreview() {
    const modal = document.getElementById('printPreviewModal');
    if (modal) {
        modal.remove();
    }
}

// تأكيد الطباعة
function confirmPrint() {
    const printContent = document.getElementById('invoicePreviewContent').innerHTML;

    // فتح نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة - ${currentInvoice.number}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body { margin: 0; padding: 20px; font-family: 'Cairo', Arial, sans-serif; }
                @media print {
                    body { padding: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            ${printContent}
            <div class="no-print" style="text-align: center; margin-top: 20px;">
                <button onclick="window.print()" style="background: #00d4aa; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;">
                    🖨️ طباعة
                </button>
                <button onclick="window.close()" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;">
                    ❌ إغلاق
                </button>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();

    // إغلاق المعاينة
    closePrintPreview();

    // إرسال للخادم
    sendToServer('print_invoice', currentInvoice);

    showNotification('تم إرسال الفاتورة للطباعة', 'success');
}

// تحميل الفاتورة كـ PDF
function downloadInvoice() {
    // إنشاء رابط تحميل
    const printContent = document.getElementById('invoicePreviewContent').innerHTML;
    const fullHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة - ${currentInvoice.number}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>body { margin: 0; padding: 20px; font-family: 'Cairo', Arial, sans-serif; }</style>
        </head>
        <body>${printContent}</body>
        </html>
    `;

    const blob = new Blob([fullHTML], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `فاتورة_${currentInvoice.number}.html`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification('تم تحميل الفاتورة', 'success');
}

// الحصول على عرض الورق حسب النوع
function getPaperWidth(paperType) {
    const paperWidths = {
        'thermal_58': 200,    // 58mm ≈ 200px
        'thermal_70': 260,    // 70mm ≈ 260px
        'thermal_80': 300,    // 80mm ≈ 300px
        'a4': 595,            // A4 width in points ≈ 595px
        'a5': 420,            // A5 width
        'letter': 612,        // Letter size
        'custom': 400         // حجم مخصص افتراضي
    };

    return paperWidths[paperType] || 300; // افتراضي 80mm
}

// الحصول على ارتفاع الورق حسب النوع
function getPaperHeight(paperType) {
    const paperHeights = {
        'thermal_58': 'auto',  // ارتفاع تلقائي للورق الحراري
        'thermal_70': 'auto',
        'thermal_80': 'auto',
        'a4': 842,             // A4 height in points
        'a5': 595,             // A5 height
        'letter': 792,         // Letter height
        'custom': 'auto'
    };

    return paperHeights[paperType] || 'auto';
}

// الحصول على HTML الشعار للفاتورة
function getLogoHTML() {
    // أولاً نحاول الحصول على شعار التصميم، ثم الشعار الرئيسي
    const designLogo = localStorage.getItem('designLogo');
    const mainLogo = localStorage.getItem('shopLogo');
    const savedLogo = designLogo || mainLogo;

    if (savedLogo) {
        return `<div style="width: 80px; height: 80px; margin: 0 auto 15px; border-radius: 50%; overflow: hidden; border: 3px solid rgba(255,255,255,0.3);">
                    <img src="${savedLogo}" style="width: 100%; height: 100%; object-fit: cover;" alt="شعار المحل">
                </div>`;
    } else {
        return `<div style="width: 60px; height: 60px; background: linear-gradient(135deg, #00d4aa, #4ecdc4); border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">🎮</div>`;
    }
}

// إنشاء HTML الشعار والعنوان للفاتورة مع المحاذاة
function getInvoiceHeaderHTML(settings) {
    const designLogo = localStorage.getItem('designLogo');
    const mainLogo = localStorage.getItem('shopLogo');
    const savedLogo = designLogo || mainLogo;

    // الحصول على إعدادات التصميم المحفوظة
    const designSettings = JSON.parse(localStorage.getItem('invoiceDesign') || '{}');
    const logoPosition = designSettings.logoPosition || 'center';
    const logoSize = designSettings.logoSize || 60;

    const shopName = designSettings.shopName || settings.shopName || 'مقهى البلايستيشن';

    if (!savedLogo) {
        return `<h1 style="margin: 0 0 10px 0; font-size: 1.8rem;">${shopName}</h1>`;
    }

    const logoImg = `<img src="${savedLogo}" style="width: ${logoSize}px; height: ${logoSize}px; object-fit: contain; border-radius: 8px; border: 2px solid rgba(255,255,255,0.3);" alt="شعار المحل">`;
    const titleText = `<h1 style="margin: 0; font-size: 1.8rem;">${shopName}</h1>`;

    switch(logoPosition) {
        case 'center':
            return `
                <div style="text-align: center; margin-bottom: 15px;">
                    <div style="margin-bottom: 10px;">${logoImg}</div>
                    ${titleText}
                </div>
            `;

        case 'right':
            return `
                <div style="text-align: right; margin-bottom: 15px;">
                    <div style="margin-bottom: 10px;">${logoImg}</div>
                    ${titleText}
                </div>
            `;

        case 'left':
            return `
                <div style="text-align: left; margin-bottom: 15px;">
                    <div style="margin-bottom: 10px;">${logoImg}</div>
                    ${titleText}
                </div>
            `;

        case 'inline-right':
            return `
                <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
                    ${logoImg}
                    ${titleText}
                </div>
            `;

        case 'inline-left':
            return `
                <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
                    ${titleText}
                    ${logoImg}
                </div>
            `;

        default:
            return `
                <div style="text-align: center; margin-bottom: 15px;">
                    <div style="margin-bottom: 10px;">${logoImg}</div>
                    ${titleText}
                </div>
            `;
    }
}

// تصدير الوظائف للاستخدام العام
window.closePrintPreview = closePrintPreview;
window.confirmPrint = confirmPrint;
window.downloadInvoice = downloadInvoice;
window.getLogoHTML = getLogoHTML;
window.getInvoiceHeaderHTML = getInvoiceHeaderHTML;
