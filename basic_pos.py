#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من نظام نقاط البيع
Basic version of POS System (works with built-in libraries only)
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime
import random

class BasicPOSApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام نقاط البيع - مقهى البلايستيشن")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')
        
        # قاعدة البيانات
        self.init_database()
        
        # متغيرات الفاتورة
        self.invoice_items = []
        self.total_amount = 0.0
        
        # إنشاء الواجهة
        self.create_interface()
        
    def init_database(self):
        """إنشاء قاعدة البيانات"""
        self.conn = sqlite3.connect('basic_pos.db')
        cursor = self.conn.cursor()
        
        # جدول الأصناف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                category TEXT DEFAULT 'عام'
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                total_amount REAL NOT NULL,
                items_text TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إضافة بيانات تجريبية
        cursor.execute('SELECT COUNT(*) FROM items')
        if cursor.fetchone()[0] == 0:
            sample_items = [
                ('ساعة بلايستيشن', 15.0, 'ألعاب'),
                ('شاي', 5.0, 'مشروبات'),
                ('قهوة', 8.0, 'مشروبات'),
                ('عصير', 10.0, 'مشروبات'),
                ('ساندويش', 20.0, 'طعام'),
                ('بيتزا صغيرة', 35.0, 'طعام'),
                ('مياه', 2.0, 'مشروبات'),
                ('كولا', 6.0, 'مشروبات')
            ]
            cursor.executemany('INSERT INTO items (name, price, category) VALUES (?, ?, ?)', sample_items)
        
        self.conn.commit()
    
    def create_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # العنوان
        title_frame = tk.Frame(self.root, bg='#34495e', height=80)
        title_frame.pack(fill='x', padx=10, pady=10)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="🎮 نظام نقاط البيع - مقهى البلايستيشن",
                              bg='#34495e', fg='white',
                              font=('Arial', 18, 'bold'))
        title_label.pack(expand=True)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # الجانب الأيسر - الأصناف
        left_frame = tk.Frame(main_frame, bg='#34495e', width=400)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))
        left_frame.pack_propagate(False)
        
        tk.Label(left_frame, text="الأصناف المتاحة", 
                bg='#34495e', fg='white', font=('Arial', 14, 'bold')).pack(pady=10)
        
        # قائمة الأصناف
        self.items_listbox = tk.Listbox(left_frame, font=('Arial', 11), height=15)
        self.items_listbox.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار الكمية والإضافة
        add_frame = tk.Frame(left_frame, bg='#34495e')
        add_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(add_frame, text="الكمية:", bg='#34495e', fg='white').pack(side='left')
        self.quantity_var = tk.StringVar(value="1")
        tk.Entry(add_frame, textvariable=self.quantity_var, width=5).pack(side='left', padx=5)
        
        tk.Button(add_frame, text="إضافة للفاتورة", 
                 command=self.add_to_invoice,
                 bg='#27ae60', fg='white', font=('Arial', 10, 'bold')).pack(side='right')
        
        # الجانب الأيمن - الفاتورة
        right_frame = tk.Frame(main_frame, bg='#34495e', width=500)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        right_frame.pack_propagate(False)
        
        tk.Label(right_frame, text="الفاتورة الحالية", 
                bg='#34495e', fg='white', font=('Arial', 14, 'bold')).pack(pady=10)
        
        # معلومات الفاتورة
        info_frame = tk.Frame(right_frame, bg='#34495e')
        info_frame.pack(fill='x', padx=10, pady=5)
        
        self.invoice_number = self.generate_invoice_number()
        tk.Label(info_frame, text=f"رقم الفاتورة: {self.invoice_number}", 
                bg='#34495e', fg='#3498db', font=('Arial', 12, 'bold')).pack(anchor='w')
        
        now = datetime.now().strftime("%Y-%m-%d %H:%M")
        tk.Label(info_frame, text=f"التاريخ: {now}", 
                bg='#34495e', fg='#3498db', font=('Arial', 12)).pack(anchor='w')
        
        # جدول الفاتورة
        columns = ('الصنف', 'السعر', 'الكمية', 'المجموع')
        self.invoice_tree = ttk.Treeview(right_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.invoice_tree.heading(col, text=col)
            self.invoice_tree.column(col, width=100, anchor='center')
        
        self.invoice_tree.pack(fill='both', expand=True, padx=10, pady=5)
        
        # المجموع
        total_frame = tk.Frame(right_frame, bg='#2c3e50')
        total_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(total_frame, text="المجموع الكلي:", 
                bg='#2c3e50', fg='white', font=('Arial', 14, 'bold')).pack(side='left')
        
        self.total_label = tk.Label(total_frame, text="0.00 ريال", 
                                   bg='#2c3e50', fg='#27ae60', font=('Arial', 16, 'bold'))
        self.total_label.pack(side='right')
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self.root, bg='#2c3e50')
        buttons_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(buttons_frame, text="💾 حفظ الفاتورة", 
                 command=self.save_invoice,
                 bg='#3498db', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='left', padx=5)
        
        tk.Button(buttons_frame, text="🖨️ طباعة نصية", 
                 command=self.print_text_invoice,
                 bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='left', padx=5)
        
        tk.Button(buttons_frame, text="📄 فاتورة جديدة", 
                 command=self.new_invoice,
                 bg='#9b59b6', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='left', padx=5)
        
        tk.Button(buttons_frame, text="📦 إدارة الأصناف", 
                 command=self.manage_items,
                 bg='#f39c12', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='right', padx=5)
        
        tk.Button(buttons_frame, text="🗑️ حذف صنف", 
                 command=self.remove_from_invoice,
                 bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='right', padx=5)
        
        # تحميل الأصناف
        self.load_items()
    
    def generate_invoice_number(self):
        """توليد رقم فاتورة"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M")
        random_num = random.randint(100, 999)
        return f"INV-{timestamp}-{random_num}"
    
    def load_items(self):
        """تحميل الأصناف"""
        self.items_listbox.delete(0, tk.END)
        cursor = self.conn.cursor()
        cursor.execute('SELECT * FROM items ORDER BY category, name')
        items = cursor.fetchall()
        
        for item in items:
            display_text = f"{item[1]} - {item[2]:.2f} ريال ({item[3]})"
            self.items_listbox.insert(tk.END, display_text)
        
        self.items_data = items
    
    def add_to_invoice(self):
        """إضافة صنف للفاتورة"""
        selection = self.items_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف")
            return
        
        try:
            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
            return
        
        selected_item = self.items_data[selection[0]]
        subtotal = selected_item[2] * quantity
        
        # إضافة للفاتورة
        invoice_item = {
            'name': selected_item[1],
            'price': selected_item[2],
            'quantity': quantity,
            'subtotal': subtotal
        }
        
        self.invoice_items.append(invoice_item)
        self.update_invoice_display()
        self.quantity_var.set("1")
    
    def remove_from_invoice(self):
        """حذف صنف من الفاتورة"""
        selection = self.invoice_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return
        
        item_index = self.invoice_tree.index(selection[0])
        if 0 <= item_index < len(self.invoice_items):
            self.invoice_items.pop(item_index)
            self.update_invoice_display()
    
    def update_invoice_display(self):
        """تحديث عرض الفاتورة"""
        # مسح الجدول
        for item in self.invoice_tree.get_children():
            self.invoice_tree.delete(item)
        
        # إضافة الأصناف
        self.total_amount = 0.0
        for item in self.invoice_items:
            self.invoice_tree.insert('', 'end', values=(
                item['name'],
                f"{item['price']:.2f}",
                item['quantity'],
                f"{item['subtotal']:.2f}"
            ))
            self.total_amount += item['subtotal']
        
        # تحديث المجموع
        self.total_label.config(text=f"{self.total_amount:.2f} ريال")
    
    def save_invoice(self):
        """حفظ الفاتورة"""
        if not self.invoice_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف في الفاتورة")
            return
        
        # تحويل الأصناف إلى نص
        items_text = "\n".join([
            f"{item['name']} x{item['quantity']} = {item['subtotal']:.2f} ريال"
            for item in self.invoice_items
        ])
        
        cursor = self.conn.cursor()
        cursor.execute('''INSERT INTO invoices (invoice_number, total_amount, items_text) 
                         VALUES (?, ?, ?)''', 
                      (self.invoice_number, self.total_amount, items_text))
        self.conn.commit()
        
        messagebox.showinfo("نجح", "تم حفظ الفاتورة بنجاح")
    
    def print_text_invoice(self):
        """طباعة الفاتورة كنص"""
        if not self.invoice_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف في الفاتورة")
            return
        
        # إنشاء نص الفاتورة
        receipt_text = []
        receipt_text.append("=" * 40)
        receipt_text.append("مقهى البلايستيشن".center(40))
        receipt_text.append("الرياض - المملكة العربية السعودية".center(40))
        receipt_text.append("=" * 40)
        receipt_text.append("")
        receipt_text.append(f"رقم الفاتورة: {self.invoice_number}")
        receipt_text.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        receipt_text.append("-" * 40)
        receipt_text.append("الصنف                الكمية    المبلغ")
        receipt_text.append("-" * 40)
        
        for item in self.invoice_items:
            name = item['name'][:20].ljust(20)
            qty = str(item['quantity']).rjust(6)
            amount = f"{item['subtotal']:.2f}".rjust(8)
            receipt_text.append(f"{name} {qty} {amount}")
        
        receipt_text.append("-" * 40)
        receipt_text.append(f"المجموع الكلي: {self.total_amount:.2f} ريال".center(40))
        receipt_text.append("=" * 40)
        receipt_text.append("")
        receipt_text.append("نسعد بخدمتكم".center(40))
        receipt_text.append("")
        
        # عرض في نافذة منفصلة
        text_window = tk.Toplevel(self.root)
        text_window.title("الفاتورة")
        text_window.geometry("500x600")
        
        text_widget = tk.Text(text_window, font=('Courier', 10), wrap='word')
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', '\n'.join(receipt_text))
        text_widget.config(state='disabled')
    
    def new_invoice(self):
        """فاتورة جديدة"""
        if self.invoice_items:
            if not messagebox.askyesno("تأكيد", "هل تريد إنشاء فاتورة جديدة؟"):
                return
        
        self.invoice_items = []
        self.total_amount = 0.0
        self.invoice_number = self.generate_invoice_number()
        self.update_invoice_display()
    
    def manage_items(self):
        """إدارة الأصناف"""
        items_window = tk.Toplevel(self.root)
        items_window.title("إدارة الأصناف")
        items_window.geometry("600x500")
        
        # نموذج إضافة صنف
        form_frame = tk.Frame(items_window)
        form_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(form_frame, text="اسم الصنف:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        name_entry = tk.Entry(form_frame, width=20)
        name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        tk.Label(form_frame, text="السعر:").grid(row=0, column=2, sticky='w', padx=5, pady=5)
        price_entry = tk.Entry(form_frame, width=10)
        price_entry.grid(row=0, column=3, padx=5, pady=5)
        
        tk.Label(form_frame, text="الفئة:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        category_var = tk.StringVar(value="عام")
        category_combo = ttk.Combobox(form_frame, textvariable=category_var,
                                     values=['مشروبات', 'طعام', 'ألعاب', 'عام'])
        category_combo.grid(row=1, column=1, padx=5, pady=5)
        
        def add_item():
            name = name_entry.get().strip()
            try:
                price = float(price_entry.get())
                category = category_var.get()
                
                if name and price > 0:
                    cursor = self.conn.cursor()
                    cursor.execute('INSERT INTO items (name, price, category) VALUES (?, ?, ?)',
                                  (name, price, category))
                    self.conn.commit()
                    
                    name_entry.delete(0, tk.END)
                    price_entry.delete(0, tk.END)
                    self.load_items()
                    messagebox.showinfo("نجح", "تم إضافة الصنف")
                else:
                    messagebox.showerror("خطأ", "يرجى إدخال بيانات صحيحة")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
        
        tk.Button(form_frame, text="إضافة", command=add_item,
                 bg='#27ae60', fg='white').grid(row=1, column=2, padx=5, pady=5)
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
        self.conn.close()

if __name__ == "__main__":
    print("🎮 تشغيل النسخة المبسطة من نظام نقاط البيع")
    print("Starting Basic POS System...")
    
    app = BasicPOSApp()
    app.run()
