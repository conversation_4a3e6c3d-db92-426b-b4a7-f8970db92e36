#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة محسنة وعصرية من نظام نقاط البيع
Enhanced modern version of POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime
import random
import math

class ModernPOSApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎮 نظام نقاط البيع - مقهى البلايستيشن")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0a0a0a')
        self.root.state('zoomed')  # ملء الشاشة

        # ألوان عصرية متدرجة
        self.colors = {
            'primary': '#1a1a2e',
            'secondary': '#16213e',
            'accent': '#0f3460',
            'success': '#00d4aa',
            'warning': '#ffb347',
            'danger': '#ff6b6b',
            'info': '#4ecdc4',
            'dark': '#0a0a0a',
            'light': '#ffffff',
            'gradient_start': '#667eea',
            'gradient_end': '#764ba2',
            'sidebar': '#1e1e2e',
            'card': '#2a2a3e'
        }

        # متغيرات الحركة
        self.animation_speed = 10
        self.sidebar_width = 280
        self.sidebar_collapsed = False

        # قاعدة البيانات
        self.init_database()

        # متغيرات الفاتورة
        self.invoice_items = []
        self.total_amount = 0.0

        # إعداد الستايل
        self.setup_modern_style()

        # إنشاء الواجهة
        self.create_modern_interface()

        # تشغيل الحركات
        self.start_animations()
        
    def init_database(self):
        """إنشاء قاعدة البيانات"""
        self.conn = sqlite3.connect('basic_pos.db')
        cursor = self.conn.cursor()
        
        # جدول الأصناف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                category TEXT DEFAULT 'عام'
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                total_amount REAL NOT NULL,
                items_text TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إضافة بيانات تجريبية
        cursor.execute('SELECT COUNT(*) FROM items')
        if cursor.fetchone()[0] == 0:
            sample_items = [
                ('ساعة بلايستيشن', 15.0, 'ألعاب'),
                ('شاي', 5.0, 'مشروبات'),
                ('قهوة', 8.0, 'مشروبات'),
                ('عصير', 10.0, 'مشروبات'),
                ('ساندويش', 20.0, 'طعام'),
                ('بيتزا صغيرة', 35.0, 'طعام'),
                ('مياه', 2.0, 'مشروبات'),
                ('كولا', 6.0, 'مشروبات')
            ]
            cursor.executemany('INSERT INTO items (name, price, category) VALUES (?, ?, ?)', sample_items)

        self.conn.commit()

    def setup_modern_style(self):
        """إعداد الستايل العصري"""
        style = ttk.Style()
        style.theme_use('clam')

        # تخصيص الأزرار
        style.configure('Modern.TButton',
                       background=self.colors['accent'],
                       foreground=self.colors['light'],
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 11, 'bold'),
                       padding=(20, 10))

        style.map('Modern.TButton',
                 background=[('active', self.colors['info']),
                           ('pressed', self.colors['success'])])

        # تخصيص الجدول
        style.configure('Modern.Treeview',
                       background=self.colors['card'],
                       foreground=self.colors['light'],
                       fieldbackground=self.colors['card'],
                       borderwidth=0,
                       font=('Segoe UI', 10))

        style.configure('Modern.Treeview.Heading',
                       background=self.colors['accent'],
                       foreground=self.colors['light'],
                       font=('Segoe UI', 11, 'bold'))

    def create_gradient_frame(self, parent, width, height, color1, color2):
        """إنشاء إطار متدرج الألوان"""
        canvas = tk.Canvas(parent, width=width, height=height, highlightthickness=0)

        # رسم التدرج
        for i in range(height):
            ratio = i / height
            r1, g1, b1 = self.hex_to_rgb(color1)
            r2, g2, b2 = self.hex_to_rgb(color2)

            r = int(r1 + (r2 - r1) * ratio)
            g = int(g1 + (g2 - g1) * ratio)
            b = int(b1 + (b2 - b1) * ratio)

            color = f'#{r:02x}{g:02x}{b:02x}'
            canvas.create_line(0, i, width, i, fill=color)

        return canvas

    def hex_to_rgb(self, hex_color):
        """تحويل اللون من hex إلى RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def create_modern_interface(self):
        """إنشاء الواجهة العصرية"""
        # الشريط العلوي المتدرج
        self.create_modern_header()

        # الإطار الرئيسي
        main_container = tk.Frame(self.root, bg=self.colors['dark'])
        main_container.pack(fill='both', expand=True)

        # المحتوى الرئيسي (يسار)
        self.main_content = tk.Frame(main_container, bg=self.colors['primary'])
        self.main_content.pack(side='left', fill='both', expand=True)

        # الشريط الجانبي (يمين)
        self.create_modern_sidebar(main_container)

        # المحتوى الرئيسي
        self.create_main_content()

        # شريط الحالة السفلي
        self.create_status_bar()

    def create_modern_header(self):
        """إنشاء الشريط العلوي العصري"""
        header_frame = tk.Frame(self.root, bg=self.colors['accent'], height=100)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # خلفية متدرجة
        gradient_canvas = self.create_gradient_frame(header_frame, 1400, 100,
                                                   self.colors['gradient_start'],
                                                   self.colors['gradient_end'])
        gradient_canvas.place(x=0, y=0)

        # محتوى الشريط العلوي
        content_frame = tk.Frame(header_frame, bg='', height=100)
        content_frame.pack(fill='both', expand=True)
        content_frame.configure(bg='')

        # العنوان الرئيسي مع تأثير الظل
        title_container = tk.Frame(content_frame, bg='')
        title_container.pack(expand=True)

        # ظل النص
        shadow_label = tk.Label(title_container,
                               text="🎮 نظام نقاط البيع - مقهى البلايستيشن",
                               bg='', fg='#000000',
                               font=('Segoe UI', 24, 'bold'))
        shadow_label.place(x=2, y=2)

        # النص الرئيسي
        self.title_label = tk.Label(title_container,
                                   text="🎮 نظام نقاط البيع - مقهى البلايستيشن",
                                   bg='', fg=self.colors['light'],
                                   font=('Segoe UI', 24, 'bold'))
        self.title_label.place(x=0, y=0)

        # معلومات الوقت والتاريخ
        self.datetime_label = tk.Label(content_frame,
                                      bg='', fg=self.colors['light'],
                                      font=('Segoe UI', 12))
        self.datetime_label.pack(side='right', padx=30, pady=30)
        self.update_datetime()

    def create_modern_sidebar(self, parent):
        """إنشاء الشريط الجانبي العصري"""
        self.sidebar = tk.Frame(parent, bg=self.colors['sidebar'], width=self.sidebar_width)
        self.sidebar.pack(side='right', fill='y')
        self.sidebar.pack_propagate(False)

        # عنوان الشريط الجانبي
        sidebar_header = tk.Frame(self.sidebar, bg=self.colors['accent'], height=60)
        sidebar_header.pack(fill='x')
        sidebar_header.pack_propagate(False)

        tk.Label(sidebar_header, text="🎛️ لوحة التحكم",
                bg=self.colors['accent'], fg=self.colors['light'],
                font=('Segoe UI', 16, 'bold')).pack(expand=True)

        # أزرار القائمة العصرية
        self.create_sidebar_buttons()

        # إحصائيات سريعة
        self.create_quick_stats()

    def create_sidebar_buttons(self):
        """إنشاء أزرار الشريط الجانبي"""
        buttons_frame = tk.Frame(self.sidebar, bg=self.colors['sidebar'])
        buttons_frame.pack(fill='x', padx=15, pady=20)

        menu_buttons = [
            ("📄", "فاتورة جديدة", self.new_invoice, self.colors['success']),
            ("💾", "حفظ الفاتورة", self.save_invoice, self.colors['info']),
            ("🖨️", "طباعة", self.print_text_invoice, self.colors['warning']),
            ("📦", "إدارة الأصناف", self.manage_items, self.colors['accent']),
            ("📊", "التقارير", self.show_reports, self.colors['gradient_start']),
            ("⚙️", "الإعدادات", self.show_settings, self.colors['secondary']),
        ]

        for icon, text, command, color in menu_buttons:
            btn_frame = tk.Frame(buttons_frame, bg=self.colors['sidebar'])
            btn_frame.pack(fill='x', pady=8)

            btn = tk.Button(btn_frame, text=f"{icon} {text}",
                           command=command,
                           bg=color, fg=self.colors['light'],
                           font=('Segoe UI', 12, 'bold'),
                           relief='flat',
                           cursor='hand2',
                           anchor='w',
                           padx=20, pady=15)
            btn.pack(fill='x')

            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn: self.on_button_hover(b, True))
            btn.bind("<Leave>", lambda e, b=btn, c=color: self.on_button_hover(b, False, c))

    def on_button_hover(self, button, is_hover, original_color=None):
        """تأثير hover للأزرار"""
        if is_hover:
            button.configure(bg=self.colors['light'], fg=self.colors['dark'])
        else:
            button.configure(bg=original_color, fg=self.colors['light'])

    def create_quick_stats(self):
        """إنشاء إحصائيات سريعة"""
        stats_frame = tk.Frame(self.sidebar, bg=self.colors['card'])
        stats_frame.pack(fill='x', padx=15, pady=20)

        tk.Label(stats_frame, text="📈 إحصائيات سريعة",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 14, 'bold')).pack(pady=10)

        # بطاقات الإحصائيات
        stats_data = [
            ("فواتير اليوم", "0", self.colors['success']),
            ("إجمالي المبيعات", "0.00 ريال", self.colors['info']),
            ("متوسط الفاتورة", "0.00 ريال", self.colors['warning'])
        ]

        for title, value, color in stats_data:
            card = tk.Frame(stats_frame, bg=color, height=60)
            card.pack(fill='x', pady=5)
            card.pack_propagate(False)

            tk.Label(card, text=title,
                    bg=color, fg=self.colors['light'],
                    font=('Segoe UI', 10, 'bold')).pack(pady=(8, 2))

            tk.Label(card, text=value,
                    bg=color, fg=self.colors['light'],
                    font=('Segoe UI', 12, 'bold')).pack()

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # تقسيم المحتوى إلى قسمين
        content_container = tk.Frame(self.main_content, bg=self.colors['primary'])
        content_container.pack(fill='both', expand=True, padx=20, pady=20)

        # القسم العلوي - الأصناف
        self.create_items_section(content_container)

        # القسم السفلي - الفاتورة
        self.create_invoice_section(content_container)

    def create_items_section(self, parent):
        """إنشاء قسم الأصناف"""
        items_frame = tk.Frame(parent, bg=self.colors['card'], height=300)
        items_frame.pack(fill='both', expand=True, pady=(0, 10))
        items_frame.pack_propagate(False)

        # عنوان القسم
        header_frame = tk.Frame(items_frame, bg=self.colors['accent'], height=50)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🛍️ الأصناف المتاحة",
                bg=self.colors['accent'], fg=self.colors['light'],
                font=('Segoe UI', 16, 'bold')).pack(side='left', padx=20, pady=10)

        # شريط البحث
        search_frame = tk.Frame(header_frame, bg=self.colors['accent'])
        search_frame.pack(side='right', padx=20, pady=10)

        tk.Label(search_frame, text="🔍",
                bg=self.colors['accent'], fg=self.colors['light'],
                font=('Segoe UI', 14)).pack(side='left', padx=(0, 5))

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=('Segoe UI', 12), width=20,
                               bg=self.colors['light'], fg=self.colors['dark'])
        search_entry.pack(side='left')
        search_entry.bind('<KeyRelease>', self.filter_items)

        # محتوى الأصناف
        content_frame = tk.Frame(items_frame, bg=self.colors['card'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # قائمة الأصناف مع تحسينات
        listbox_frame = tk.Frame(content_frame, bg=self.colors['card'])
        listbox_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        self.items_listbox = tk.Listbox(listbox_frame,
                                       font=('Segoe UI', 11),
                                       bg=self.colors['light'],
                                       fg=self.colors['dark'],
                                       selectbackground=self.colors['info'],
                                       selectforeground=self.colors['light'],
                                       borderwidth=0,
                                       highlightthickness=0)
        self.items_listbox.pack(fill='both', expand=True)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(listbox_frame, orient='vertical', command=self.items_listbox.yview)
        self.items_listbox.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # لوحة التحكم في الإضافة
        control_frame = tk.Frame(content_frame, bg=self.colors['secondary'], width=200)
        control_frame.pack(side='right', fill='y')
        control_frame.pack_propagate(False)

        tk.Label(control_frame, text="⚡ إضافة سريعة",
                bg=self.colors['secondary'], fg=self.colors['light'],
                font=('Segoe UI', 14, 'bold')).pack(pady=20)

        # حقل الكمية مع تحسينات
        qty_frame = tk.Frame(control_frame, bg=self.colors['secondary'])
        qty_frame.pack(pady=20)

        tk.Label(qty_frame, text="الكمية:",
                bg=self.colors['secondary'], fg=self.colors['light'],
                font=('Segoe UI', 12, 'bold')).pack()

        self.quantity_var = tk.StringVar(value="1")
        qty_entry = tk.Entry(qty_frame, textvariable=self.quantity_var,
                            font=('Segoe UI', 14, 'bold'),
                            width=8, justify='center',
                            bg=self.colors['light'], fg=self.colors['dark'])
        qty_entry.pack(pady=10)

        # أزرار الكمية
        qty_buttons_frame = tk.Frame(qty_frame, bg=self.colors['secondary'])
        qty_buttons_frame.pack()

        tk.Button(qty_buttons_frame, text="➖",
                 command=self.decrease_quantity,
                 bg=self.colors['danger'], fg=self.colors['light'],
                 font=('Segoe UI', 12, 'bold'),
                 width=3, relief='flat').pack(side='left', padx=2)

        tk.Button(qty_buttons_frame, text="➕",
                 command=self.increase_quantity,
                 bg=self.colors['success'], fg=self.colors['light'],
                 font=('Segoe UI', 12, 'bold'),
                 width=3, relief='flat').pack(side='left', padx=2)

        # زر الإضافة الكبير
        add_button = tk.Button(control_frame, text="🛒\nإضافة للفاتورة",
                              command=self.add_to_invoice,
                              bg=self.colors['success'], fg=self.colors['light'],
                              font=('Segoe UI', 14, 'bold'),
                              relief='flat', cursor='hand2',
                              height=3)
        add_button.pack(pady=30, padx=20, fill='x')

        # تأثير hover للزر
        add_button.bind("<Enter>", lambda e: add_button.configure(bg=self.colors['info']))
        add_button.bind("<Leave>", lambda e: add_button.configure(bg=self.colors['success']))

    def create_invoice_section(self, parent):
        """إنشاء قسم الفاتورة"""
        invoice_frame = tk.Frame(parent, bg=self.colors['card'], height=400)
        invoice_frame.pack(fill='both', expand=True)
        invoice_frame.pack_propagate(False)

        # عنوان القسم
        header_frame = tk.Frame(invoice_frame, bg=self.colors['accent'], height=50)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🧾 الفاتورة الحالية",
                bg=self.colors['accent'], fg=self.colors['light'],
                font=('Segoe UI', 16, 'bold')).pack(side='left', padx=20, pady=10)

        # معلومات الفاتورة
        info_frame = tk.Frame(header_frame, bg=self.colors['accent'])
        info_frame.pack(side='right', padx=20, pady=10)

        self.invoice_number = self.generate_invoice_number()
        tk.Label(info_frame, text=f"رقم الفاتورة: {self.invoice_number}",
                bg=self.colors['accent'], fg=self.colors['info'],
                font=('Segoe UI', 12, 'bold')).pack()

        # محتوى الفاتورة
        content_frame = tk.Frame(invoice_frame, bg=self.colors['card'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول الفاتورة مع تحسينات
        table_frame = tk.Frame(content_frame, bg=self.colors['card'])
        table_frame.pack(fill='both', expand=True, padx=(0, 10))

        columns = ('الصنف', 'السعر', 'الكمية', 'المجموع')
        self.invoice_tree = ttk.Treeview(table_frame, columns=columns, show='headings',
                                        height=10, style='Modern.Treeview')

        # تخصيص الأعمدة
        column_widths = {'الصنف': 200, 'السعر': 100, 'الكمية': 80, 'المجموع': 120}
        for col in columns:
            self.invoice_tree.heading(col, text=col)
            self.invoice_tree.column(col, width=column_widths[col], anchor='center')

        # شريط التمرير للجدول
        tree_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.invoice_tree.yview)
        self.invoice_tree.configure(yscrollcommand=tree_scrollbar.set)

        self.invoice_tree.pack(side='left', fill='both', expand=True)
        tree_scrollbar.pack(side='right', fill='y')

        # لوحة التحكم في الفاتورة
        control_frame = tk.Frame(content_frame, bg=self.colors['secondary'], width=200)
        control_frame.pack(side='right', fill='y')
        control_frame.pack_propagate(False)

        tk.Label(control_frame, text="🎛️ التحكم",
                bg=self.colors['secondary'], fg=self.colors['light'],
                font=('Segoe UI', 14, 'bold')).pack(pady=20)

        # زر حذف الصنف
        delete_btn = tk.Button(control_frame, text="🗑️\nحذف الصنف",
                              command=self.remove_from_invoice,
                              bg=self.colors['danger'], fg=self.colors['light'],
                              font=('Segoe UI', 12, 'bold'),
                              relief='flat', cursor='hand2',
                              height=3)
        delete_btn.pack(pady=10, padx=20, fill='x')

        # المجموع الكلي مع تصميم جذاب
        total_container = tk.Frame(control_frame, bg=self.colors['success'], height=80)
        total_container.pack(fill='x', padx=20, pady=20)
        total_container.pack_propagate(False)

        tk.Label(total_container, text="المجموع الكلي",
                bg=self.colors['success'], fg=self.colors['light'],
                font=('Segoe UI', 12, 'bold')).pack(pady=(10, 0))

        self.total_label = tk.Label(total_container, text="0.00 ريال",
                                   bg=self.colors['success'], fg=self.colors['light'],
                                   font=('Segoe UI', 16, 'bold'))
        self.total_label.pack()

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.root, bg=self.colors['accent'], height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(status_frame, text="🟢 جاهز للعمل",
                                    bg=self.colors['accent'], fg=self.colors['light'],
                                    font=('Segoe UI', 10))
        self.status_label.pack(side='left', padx=20, pady=5)

        # معلومات النظام
        system_info = tk.Label(status_frame, text="نظام نقاط البيع v2.0 | مقهى البلايستيشن",
                              bg=self.colors['accent'], fg=self.colors['info'],
                              font=('Segoe UI', 10))
        system_info.pack(side='right', padx=20, pady=5)

        # تحميل الأصناف
        self.load_items()

    def start_animations(self):
        """بدء الحركات والتأثيرات"""
        self.animate_title()
        self.update_datetime()

    def animate_title(self):
        """تحريك العنوان"""
        colors = [self.colors['light'], self.colors['info'], self.colors['success'],
                 self.colors['warning'], self.colors['light']]

        def change_color(index=0):
            if hasattr(self, 'title_label'):
                self.title_label.configure(fg=colors[index % len(colors)])
                self.root.after(2000, lambda: change_color(index + 1))

        change_color()

    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        if hasattr(self, 'datetime_label'):
            now = datetime.now()
            datetime_str = now.strftime("📅 %Y-%m-%d | ⏰ %H:%M:%S")
            self.datetime_label.config(text=datetime_str)
            self.root.after(1000, self.update_datetime)
    
    def generate_invoice_number(self):
        """توليد رقم فاتورة"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M")
        random_num = random.randint(100, 999)
        return f"INV-{timestamp}-{random_num}"
    
    def filter_items(self, event=None):
        """تصفية الأصناف حسب البحث"""
        search_term = self.search_var.get().lower()
        self.items_listbox.delete(0, tk.END)

        cursor = self.conn.cursor()
        cursor.execute('SELECT * FROM items ORDER BY category, name')
        items = cursor.fetchall()

        filtered_items = []
        for item in items:
            if search_term in item[1].lower() or search_term in item[3].lower():
                display_text = f"🏷️ {item[1]} - {item[2]:.2f} ريال ({item[3]})"
                self.items_listbox.insert(tk.END, display_text)
                filtered_items.append(item)

        self.items_data = filtered_items

    def load_items(self):
        """تحميل الأصناف"""
        self.items_listbox.delete(0, tk.END)
        cursor = self.conn.cursor()
        cursor.execute('SELECT * FROM items ORDER BY category, name')
        items = cursor.fetchall()

        for item in items:
            display_text = f"🏷️ {item[1]} - {item[2]:.2f} ريال ({item[3]})"
            self.items_listbox.insert(tk.END, display_text)

        self.items_data = items
        self.status_label.config(text=f"🟢 تم تحميل {len(items)} صنف")

    def increase_quantity(self):
        """زيادة الكمية"""
        try:
            current = int(self.quantity_var.get())
            self.quantity_var.set(str(current + 1))
        except ValueError:
            self.quantity_var.set("1")

    def decrease_quantity(self):
        """تقليل الكمية"""
        try:
            current = int(self.quantity_var.get())
            if current > 1:
                self.quantity_var.set(str(current - 1))
        except ValueError:
            self.quantity_var.set("1")
    
    def add_to_invoice(self):
        """إضافة صنف للفاتورة مع تأثيرات بصرية"""
        selection = self.items_listbox.curselection()
        if not selection:
            self.status_label.config(text="⚠️ يرجى اختيار صنف من القائمة")
            messagebox.showwarning("تحذير", "يرجى اختيار صنف")
            return

        try:
            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            self.status_label.config(text="❌ كمية غير صحيحة")
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
            return

        selected_item = self.items_data[selection[0]]
        subtotal = selected_item[2] * quantity

        # البحث عن الصنف في الفاتورة الحالية
        existing_item = None
        for i, item in enumerate(self.invoice_items):
            if item['name'] == selected_item[1]:
                existing_item = i
                break

        if existing_item is not None:
            # تحديث الكمية إذا كان الصنف موجود
            self.invoice_items[existing_item]['quantity'] += quantity
            self.invoice_items[existing_item]['subtotal'] = (
                self.invoice_items[existing_item]['price'] *
                self.invoice_items[existing_item]['quantity']
            )
            self.status_label.config(text=f"✅ تم تحديث كمية {selected_item[1]}")
        else:
            # إضافة صنف جديد
            invoice_item = {
                'name': selected_item[1],
                'price': selected_item[2],
                'quantity': quantity,
                'subtotal': subtotal
            }
            self.invoice_items.append(invoice_item)
            self.status_label.config(text=f"✅ تم إضافة {selected_item[1]} للفاتورة")

        self.update_invoice_display()
        self.quantity_var.set("1")

        # تأثير بصري
        self.animate_add_success()
    
    def animate_add_success(self):
        """تأثير بصري عند إضافة صنف"""
        original_bg = self.total_label.cget('bg')

        def flash(count=0):
            if count < 6:
                color = self.colors['warning'] if count % 2 == 0 else original_bg
                self.total_label.configure(bg=color)
                self.root.after(100, lambda: flash(count + 1))
            else:
                self.total_label.configure(bg=original_bg)

        flash()

    def remove_from_invoice(self):
        """حذف صنف من الفاتورة مع تأكيد"""
        selection = self.invoice_tree.selection()
        if not selection:
            self.status_label.config(text="⚠️ يرجى اختيار صنف للحذف")
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return

        item_index = self.invoice_tree.index(selection[0])
        if 0 <= item_index < len(self.invoice_items):
            item_name = self.invoice_items[item_index]['name']

            if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف '{item_name}' من الفاتورة؟"):
                self.invoice_items.pop(item_index)
                self.update_invoice_display()
                self.status_label.config(text=f"🗑️ تم حذف {item_name}")

    def show_reports(self):
        """عرض التقارير"""
        reports_window = tk.Toplevel(self.root)
        reports_window.title("📊 التقارير والإحصائيات")
        reports_window.geometry("800x600")
        reports_window.configure(bg=self.colors['primary'])

        # عنوان النافذة
        title_frame = tk.Frame(reports_window, bg=self.colors['accent'], height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="📊 التقارير والإحصائيات",
                bg=self.colors['accent'], fg=self.colors['light'],
                font=('Segoe UI', 18, 'bold')).pack(expand=True)

        # محتوى التقارير
        content_frame = tk.Frame(reports_window, bg=self.colors['primary'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # إحصائيات سريعة
        cursor = self.conn.cursor()
        cursor.execute('SELECT COUNT(*), SUM(total_amount) FROM invoices')
        stats = cursor.fetchone()

        total_invoices = stats[0] if stats[0] else 0
        total_sales = stats[1] if stats[1] else 0.0

        stats_frame = tk.Frame(content_frame, bg=self.colors['card'])
        stats_frame.pack(fill='x', pady=(0, 20))

        tk.Label(stats_frame, text=f"📄 إجمالي الفواتير: {total_invoices}",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 14, 'bold')).pack(pady=10)

        tk.Label(stats_frame, text=f"💰 إجمالي المبيعات: {total_sales:.2f} ريال",
                bg=self.colors['card'], fg=self.colors['success'],
                font=('Segoe UI', 14, 'bold')).pack(pady=10)

        # قائمة الفواتير
        tk.Label(content_frame, text="📋 آخر الفواتير:",
                bg=self.colors['primary'], fg=self.colors['light'],
                font=('Segoe UI', 16, 'bold')).pack(anchor='w', pady=(0, 10))

        # جدول الفواتير
        columns = ('رقم الفاتورة', 'المبلغ', 'التاريخ')
        reports_tree = ttk.Treeview(content_frame, columns=columns, show='headings',
                                   height=15, style='Modern.Treeview')

        for col in columns:
            reports_tree.heading(col, text=col)
            reports_tree.column(col, width=200, anchor='center')

        # تحميل البيانات
        cursor.execute('SELECT invoice_number, total_amount, created_at FROM invoices ORDER BY created_at DESC LIMIT 20')
        invoices = cursor.fetchall()

        for invoice in invoices:
            reports_tree.insert('', 'end', values=(
                invoice[0],
                f"{invoice[1]:.2f} ريال",
                invoice[2][:16]
            ))

        reports_tree.pack(fill='both', expand=True)

    def show_settings(self):
        """عرض الإعدادات"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("⚙️ الإعدادات")
        settings_window.geometry("600x400")
        settings_window.configure(bg=self.colors['primary'])

        # عنوان النافذة
        title_frame = tk.Frame(settings_window, bg=self.colors['accent'], height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="⚙️ إعدادات النظام",
                bg=self.colors['accent'], fg=self.colors['light'],
                font=('Segoe UI', 18, 'bold')).pack(expand=True)

        # محتوى الإعدادات
        content_frame = tk.Frame(settings_window, bg=self.colors['primary'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # إعدادات المحل
        shop_frame = tk.Frame(content_frame, bg=self.colors['card'])
        shop_frame.pack(fill='x', pady=10)

        tk.Label(shop_frame, text="🏪 معلومات المحل",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 14, 'bold')).pack(pady=10)

        tk.Label(shop_frame, text="اسم المحل: مقهى البلايستيشن",
                bg=self.colors['card'], fg=self.colors['info'],
                font=('Segoe UI', 12)).pack(pady=5)

        tk.Label(shop_frame, text="العنوان: الرياض - المملكة العربية السعودية",
                bg=self.colors['card'], fg=self.colors['info'],
                font=('Segoe UI', 12)).pack(pady=5)

        # إعدادات النظام
        system_frame = tk.Frame(content_frame, bg=self.colors['card'])
        system_frame.pack(fill='x', pady=10)

        tk.Label(system_frame, text="💻 معلومات النظام",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 14, 'bold')).pack(pady=10)

        tk.Label(system_frame, text="الإصدار: 2.0 Enhanced",
                bg=self.colors['card'], fg=self.colors['info'],
                font=('Segoe UI', 12)).pack(pady=5)

        tk.Label(system_frame, text="المطور: مساعد الذكي الاصطناعي",
                bg=self.colors['card'], fg=self.colors['info'],
                font=('Segoe UI', 12)).pack(pady=5)
    
    def update_invoice_display(self):
        """تحديث عرض الفاتورة مع تأثيرات بصرية"""
        # مسح الجدول
        for item in self.invoice_tree.get_children():
            self.invoice_tree.delete(item)

        # إضافة الأصناف مع ألوان متناوبة
        self.total_amount = 0.0
        for i, item in enumerate(self.invoice_items):
            # تلوين الصفوف بالتناوب
            tags = ('evenrow',) if i % 2 == 0 else ('oddrow',)

            item_id = self.invoice_tree.insert('', 'end', values=(
                f"🏷️ {item['name']}",
                f"{item['price']:.2f} ريال",
                f"✖️ {item['quantity']}",
                f"💰 {item['subtotal']:.2f} ريال"
            ), tags=tags)

            self.total_amount += item['subtotal']

        # تخصيص ألوان الصفوف
        self.invoice_tree.tag_configure('evenrow', background=self.colors['card'])
        self.invoice_tree.tag_configure('oddrow', background=self.colors['secondary'])

        # تحديث المجموع مع تأثير
        self.animate_total_update()

    def animate_total_update(self):
        """تحريك تحديث المجموع"""
        self.total_label.config(text=f"💰 {self.total_amount:.2f} ريال")

        # تأثير نبضة
        original_font = self.total_label.cget('font')

        def pulse(count=0):
            if count < 4:
                size = 18 if count % 2 == 0 else 16
                self.total_label.configure(font=('Segoe UI', size, 'bold'))
                self.root.after(150, lambda: pulse(count + 1))
            else:
                self.total_label.configure(font=('Segoe UI', 16, 'bold'))

        pulse()
    
    def save_invoice(self):
        """حفظ الفاتورة مع تأثيرات بصرية"""
        if not self.invoice_items:
            self.status_label.config(text="⚠️ لا توجد أصناف في الفاتورة")
            messagebox.showwarning("تحذير", "لا توجد أصناف في الفاتورة")
            return

        # تحويل الأصناف إلى نص
        items_text = "\n".join([
            f"{item['name']} x{item['quantity']} = {item['subtotal']:.2f} ريال"
            for item in self.invoice_items
        ])

        try:
            cursor = self.conn.cursor()
            cursor.execute('''INSERT INTO invoices (invoice_number, total_amount, items_text)
                             VALUES (?, ?, ?)''',
                          (self.invoice_number, self.total_amount, items_text))
            self.conn.commit()

            self.status_label.config(text=f"✅ تم حفظ الفاتورة {self.invoice_number}")
            messagebox.showinfo("نجح", f"تم حفظ الفاتورة {self.invoice_number} بنجاح")

            # تأثير بصري للنجاح
            self.animate_save_success()

        except Exception as e:
            self.status_label.config(text="❌ خطأ في حفظ الفاتورة")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

    def animate_save_success(self):
        """تأثير بصري عند الحفظ الناجح"""
        # تأثير وميض أخضر
        original_bg = self.root.cget('bg')

        def flash_green(count=0):
            if count < 4:
                bg_color = self.colors['success'] if count % 2 == 0 else original_bg
                # يمكن إضافة تأثيرات أخرى هنا
                self.root.after(200, lambda: flash_green(count + 1))

        flash_green()
    
    def print_text_invoice(self):
        """طباعة الفاتورة كنص"""
        if not self.invoice_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف في الفاتورة")
            return
        
        # إنشاء نص الفاتورة
        receipt_text = []
        receipt_text.append("=" * 40)
        receipt_text.append("مقهى البلايستيشن".center(40))
        receipt_text.append("الرياض - المملكة العربية السعودية".center(40))
        receipt_text.append("=" * 40)
        receipt_text.append("")
        receipt_text.append(f"رقم الفاتورة: {self.invoice_number}")
        receipt_text.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        receipt_text.append("-" * 40)
        receipt_text.append("الصنف                الكمية    المبلغ")
        receipt_text.append("-" * 40)
        
        for item in self.invoice_items:
            name = item['name'][:20].ljust(20)
            qty = str(item['quantity']).rjust(6)
            amount = f"{item['subtotal']:.2f}".rjust(8)
            receipt_text.append(f"{name} {qty} {amount}")
        
        receipt_text.append("-" * 40)
        receipt_text.append(f"المجموع الكلي: {self.total_amount:.2f} ريال".center(40))
        receipt_text.append("=" * 40)
        receipt_text.append("")
        receipt_text.append("نسعد بخدمتكم".center(40))
        receipt_text.append("")
        
        # عرض في نافذة منفصلة
        text_window = tk.Toplevel(self.root)
        text_window.title("الفاتورة")
        text_window.geometry("500x600")
        
        text_widget = tk.Text(text_window, font=('Courier', 10), wrap='word')
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', '\n'.join(receipt_text))
        text_widget.config(state='disabled')
    
    def new_invoice(self):
        """إنشاء فاتورة جديدة مع تأثيرات"""
        if self.invoice_items:
            if not messagebox.askyesno("تأكيد", "هل تريد إنشاء فاتورة جديدة؟\nسيتم فقدان البيانات الحالية."):
                return

        # تأثير انتقالي
        self.animate_new_invoice()

        self.invoice_items = []
        self.total_amount = 0.0
        self.invoice_number = self.generate_invoice_number()
        self.quantity_var.set("1")
        self.search_var.set("")

        # تحديث رقم الفاتورة في الواجهة
        for widget in self.root.winfo_children():
            if hasattr(widget, 'winfo_children'):
                self.update_invoice_number_display(widget)

        self.update_invoice_display()
        self.load_items()
        self.status_label.config(text=f"📄 فاتورة جديدة: {self.invoice_number}")

    def animate_new_invoice(self):
        """تأثير بصري للفاتورة الجديدة"""
        # تأثير مسح سريع
        for item in self.invoice_tree.get_children():
            self.invoice_tree.delete(item)

        # تأثير وميض
        original_bg = self.total_label.cget('bg')

        def clear_flash(count=0):
            if count < 6:
                bg_color = self.colors['warning'] if count % 2 == 0 else original_bg
                self.total_label.configure(bg=bg_color)
                self.root.after(100, lambda: clear_flash(count + 1))
            else:
                self.total_label.configure(bg=original_bg)

        clear_flash()

    def update_invoice_number_display(self, widget):
        """تحديث رقم الفاتورة في الواجهة"""
        try:
            for child in widget.winfo_children():
                if isinstance(child, tk.Label) and "رقم الفاتورة:" in child.cget('text'):
                    child.config(text=f"رقم الفاتورة: {self.invoice_number}")
                elif hasattr(child, 'winfo_children'):
                    self.update_invoice_number_display(child)
        except:
            pass
    
    def manage_items(self):
        """إدارة الأصناف مع واجهة عصرية"""
        items_window = tk.Toplevel(self.root)
        items_window.title("📦 إدارة الأصناف")
        items_window.geometry("800x600")
        items_window.configure(bg=self.colors['primary'])

        # عنوان النافذة
        title_frame = tk.Frame(items_window, bg=self.colors['accent'], height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="📦 إدارة الأصناف والأسعار",
                bg=self.colors['accent'], fg=self.colors['light'],
                font=('Segoe UI', 18, 'bold')).pack(expand=True)

        # نموذج إضافة صنف
        form_frame = tk.Frame(items_window, bg=self.colors['card'])
        form_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(form_frame, text="➕ إضافة صنف جديد",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 14, 'bold')).pack(pady=10)

        # حقول الإدخال
        fields_frame = tk.Frame(form_frame, bg=self.colors['card'])
        fields_frame.pack(pady=10)

        # اسم الصنف
        tk.Label(fields_frame, text="اسم الصنف:",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 12, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=5)

        name_entry = tk.Entry(fields_frame, width=25, font=('Segoe UI', 12),
                             bg=self.colors['light'], fg=self.colors['dark'])
        name_entry.grid(row=0, column=1, padx=5, pady=5)

        # السعر
        tk.Label(fields_frame, text="السعر (ريال):",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 12, 'bold')).grid(row=0, column=2, sticky='w', padx=5, pady=5)

        price_entry = tk.Entry(fields_frame, width=15, font=('Segoe UI', 12),
                              bg=self.colors['light'], fg=self.colors['dark'])
        price_entry.grid(row=0, column=3, padx=5, pady=5)

        # الفئة
        tk.Label(fields_frame, text="الفئة:",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 12, 'bold')).grid(row=1, column=0, sticky='w', padx=5, pady=5)

        category_var = tk.StringVar(value="عام")
        category_combo = ttk.Combobox(fields_frame, textvariable=category_var,
                                     values=['مشروبات', 'طعام', 'ألعاب', 'عام'],
                                     font=('Segoe UI', 12), width=22)
        category_combo.grid(row=1, column=1, padx=5, pady=5)

        def add_item():
            name = name_entry.get().strip()
            try:
                price = float(price_entry.get())
                category = category_var.get()

                if name and price > 0:
                    cursor = self.conn.cursor()
                    cursor.execute('INSERT INTO items (name, price, category) VALUES (?, ?, ?)',
                                  (name, price, category))
                    self.conn.commit()

                    name_entry.delete(0, tk.END)
                    price_entry.delete(0, tk.END)
                    category_var.set("عام")
                    self.load_items()
                    self.status_label.config(text=f"✅ تم إضافة {name}")
                    messagebox.showinfo("نجح", f"تم إضافة '{name}' بنجاح")
                else:
                    messagebox.showerror("خطأ", "يرجى إدخال بيانات صحيحة")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")

        # زر الإضافة
        add_button = tk.Button(fields_frame, text="➕ إضافة الصنف",
                              command=add_item,
                              bg=self.colors['success'], fg=self.colors['light'],
                              font=('Segoe UI', 12, 'bold'),
                              relief='flat', cursor='hand2',
                              padx=20, pady=10)
        add_button.grid(row=1, column=2, columnspan=2, padx=5, pady=15)

        # قائمة الأصناف الحالية
        list_frame = tk.Frame(items_window, bg=self.colors['card'])
        list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        tk.Label(list_frame, text="📋 الأصناف الحالية",
                bg=self.colors['card'], fg=self.colors['light'],
                font=('Segoe UI', 14, 'bold')).pack(pady=10)

        # جدول الأصناف
        columns = ('الاسم', 'السعر', 'الفئة')
        items_tree = ttk.Treeview(list_frame, columns=columns, show='headings',
                                 height=12, style='Modern.Treeview')

        for col in columns:
            items_tree.heading(col, text=col)
            items_tree.column(col, width=200, anchor='center')

        # تحميل البيانات
        cursor = self.conn.cursor()
        cursor.execute('SELECT name, price, category FROM items ORDER BY category, name')
        items = cursor.fetchall()

        for item in items:
            items_tree.insert('', 'end', values=(
                f"🏷️ {item[0]}",
                f"{item[1]:.2f} ريال",
                f"📂 {item[2]}"
            ))

        items_tree.pack(fill='both', expand=True, padx=10, pady=(0, 10))
    
    def run(self):
        """تشغيل التطبيق مع معالجة الأخطاء"""
        try:
            # رسالة ترحيب
            self.status_label.config(text="🚀 مرحباً بك في نظام نقاط البيع")

            # تشغيل الحلقة الرئيسية
            self.root.mainloop()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التطبيق: {str(e)}")
        finally:
            # إغلاق قاعدة البيانات
            if hasattr(self, 'conn'):
                self.conn.close()

if __name__ == "__main__":
    print("🎮 تشغيل النسخة العصرية من نظام نقاط البيع")
    print("Starting Modern POS System...")
    print("=" * 50)
    print("✨ الميزات الجديدة:")
    print("   🎨 واجهة عصرية ومتحركة")
    print("   📱 قائمة جانبية في اليمين")
    print("   🌈 ألوان متدرجة وجذابة")
    print("   ⚡ تأثيرات بصرية متقدمة")
    print("   🔍 بحث سريع في الأصناف")
    print("   📊 إحصائيات فورية")
    print("=" * 50)

    try:
        app = ModernPOSApp()
        app.run()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")
