@echo off
chcp 65001 >nul
title نظام نقاط البيع المتقدم - مقهى البلايستيشن

echo.
echo ========================================
echo 🎮 نظام نقاط البيع المتقدم
echo PlayStation Cafe Advanced POS System
echo ========================================
echo ✨ الإصدار 2.0 Enhanced
echo 📅 %date% - ⏰ %time%
echo ========================================
echo.

REM التحقق من وجود Python
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo ❌ Python is not installed
    echo.
    echo 💡 يرجى تحميل وتثبيت Python من:
    echo Please download and install Python from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

echo.
echo 🔍 فحص المكتبات المطلوبة...

REM فحص المكتبات الأساسية
python -c "import tkinter" 2>nul
if errorlevel 1 (
    echo ❌ tkinter مفقود
    goto :install_deps
)

python -c "import sqlite3" 2>nul
if errorlevel 1 (
    echo ❌ sqlite3 مفقود
    goto :install_deps
)

echo ✅ المكتبات الأساسية متوفرة

echo.
echo 🚀 تشغيل النظام المتقدم...
echo Starting Advanced POS System...

REM محاولة تشغيل النسخة المتقدمة
python basic_pos.py
if errorlevel 1 (
    echo.
    echo ⚠️ فشل في تشغيل النسخة المتقدمة
    echo Failed to run advanced version
    echo.
    echo 🔄 محاولة تشغيل النسخة البسيطة...
    echo Trying to run basic version...
    
    REM إنشاء نسخة بسيطة جداً
    echo import tkinter as tk > simple_pos.py
    echo from tkinter import messagebox >> simple_pos.py
    echo. >> simple_pos.py
    echo root = tk.Tk() >> simple_pos.py
    echo root.title("نظام نقاط البيع البسيط") >> simple_pos.py
    echo root.geometry("800x600") >> simple_pos.py
    echo. >> simple_pos.py
    echo tk.Label(root, text="🎮 مقهى البلايستيشن", font=("Arial", 20)).pack(pady=50) >> simple_pos.py
    echo tk.Label(root, text="نظام نقاط البيع", font=("Arial", 16)).pack(pady=20) >> simple_pos.py
    echo tk.Button(root, text="إغلاق", command=root.quit, font=("Arial", 14)).pack(pady=20) >> simple_pos.py
    echo. >> simple_pos.py
    echo root.mainloop() >> simple_pos.py
    
    python simple_pos.py
    del simple_pos.py
)

goto :end

:install_deps
echo.
echo 📦 تثبيت المكتبات المطلوبة...
echo Installing required dependencies...

set /p choice="هل تريد تثبيت المكتبات المطلوبة؟ (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🔄 تثبيت المكتبات...
    
    REM تثبيت المكتبات الأساسية
    echo 📦 تثبيت Pillow...
    pip install Pillow --quiet
    
    echo 📦 تثبيت reportlab...
    pip install reportlab --quiet
    
    echo 📦 تثبيت arabic-reshaper...
    pip install arabic-reshaper --quiet
    
    echo 📦 تثبيت python-bidi...
    pip install python-bidi --quiet
    
    echo 📦 تثبيت pywin32...
    pip install pywin32 --quiet
    
    echo.
    echo ✅ تم تثبيت المكتبات
    echo Libraries installed successfully
    
    echo.
    echo 🔄 محاولة تشغيل البرنامج مرة أخرى...
    python basic_pos.py
) else (
    echo.
    echo ℹ️ يمكنك تثبيت المكتبات لاحقاً باستخدام:
    echo You can install dependencies later using:
    echo python install.py
)

:end
echo.
echo ========================================
echo 🎮 شكراً لاستخدام نظام نقاط البيع
echo Thank you for using POS System
echo ========================================
echo.
pause
