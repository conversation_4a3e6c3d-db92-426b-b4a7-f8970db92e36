// الميزات المتقدمة لنظام نقاط البيع
// Advanced Features for POS System

// متغيرات عامة للميزات المتقدمة
let currentDesign = {};
let availablePrinters = [];
let printSettings = {};
let invoiceDesign = {};

// تهيئة الميزات المتقدمة
document.addEventListener('DOMContentLoaded', function() {
    initAdvancedFeatures();
});

function initAdvancedFeatures() {
    loadPrinterSettings();
    loadInvoiceDesign();
    setupAdvancedEventListeners();
}

// إعداد مستمعي الأحداث المتقدمة
function setupAdvancedEventListeners() {
    // تبويبات تصميم الفاتورة
    setupDesignTabs();
    
    // إعدادات الطباعة
    setupPrinterEvents();
    
    // معاينة الفاتورة
    setupPreviewEvents();
}

// === وظائف تصميم الفاتورة ===

function showTab(tabId) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // عرض التبويب المحدد
    const targetTab = document.getElementById(tabId);
    if (targetTab) {
        targetTab.classList.add('active');
    }
    
    // تفعيل زر التبويب
    event.target.classList.add('active');
    
    // تحميل محتوى التبويب
    loadTabContent(tabId);
}

function loadTabContent(tabId) {
    switch(tabId) {
        case 'design-layout':
            setupLayoutControls();
            break;
        case 'design-content':
            setupContentControls();
            break;
        case 'design-style':
            setupStyleControls();
            break;
        case 'design-preview':
            updatePreview();
            break;
        case 'printer-selection':
            loadPrinters();
            break;
        case 'print-options':
            setupPrintOptions();
            break;
        case 'receipt-types':
            setupReceiptTypes();
            break;
        case 'print-test':
            setupPrintTest();
            break;
    }
}

function setupDesignTabs() {
    // إعداد التحكم في نوع الورق
    const paperTypeSelect = document.getElementById('paperType');
    if (paperTypeSelect) {
        paperTypeSelect.addEventListener('change', function() {
            const customDimensions = document.getElementById('customDimensions');
            if (this.value === 'custom') {
                customDimensions.style.display = 'block';
            } else {
                customDimensions.style.display = 'none';
            }
            updatePreview();
        });
    }
    
    // إعداد أشرطة التمرير
    setupRangeSliders();
    
    // إعداد قائمة العناصر القابلة للسحب
    setupSortableElements();
}

function setupRangeSliders() {
    const sliders = [
        { id: 'titleFontSize', valueId: 'titleFontSizeValue', suffix: 'px' },
        { id: 'contentFontSize', valueId: 'contentFontSizeValue', suffix: 'px' },
        { id: 'backgroundOpacity', valueId: 'backgroundOpacityValue', suffix: '%' },
        { id: 'previewScale', valueId: 'previewScaleValue', suffix: '%' },
        { id: 'printDensity', valueId: 'printDensityValue', suffix: '' },
        { id: 'lineSpacing', valueId: 'lineSpacingValue', suffix: '' }
    ];
    
    sliders.forEach(slider => {
        const element = document.getElementById(slider.id);
        const valueElement = document.getElementById(slider.valueId);
        
        if (element && valueElement) {
            element.addEventListener('input', function() {
                valueElement.textContent = this.value + slider.suffix;
                updatePreview();
            });
        }
    });
}

function setupSortableElements() {
    // إعداد قائمة العناصر القابلة للسحب والإفلات
    const sortableList = document.getElementById('elementsOrder');
    if (sortableList) {
        // يمكن إضافة مكتبة Sortable.js هنا للسحب والإفلات
        console.log('إعداد العناصر القابلة للسحب');
    }
}

function updatePreview() {
    const previewContainer = document.getElementById('invoicePreview');
    if (!previewContainer) return;
    
    // جمع إعدادات التصميم
    const design = collectDesignSettings();
    
    // إنشاء معاينة الفاتورة
    const previewHTML = generatePreviewHTML(design);
    previewContainer.innerHTML = previewHTML;
    
    // تطبيق المقياس
    const scale = document.getElementById('previewScale')?.value || 100;
    previewContainer.style.transform = `scale(${scale / 100})`;
}

function collectDesignSettings() {
    return {
        paperType: document.getElementById('paperType')?.value || 'thermal_80',
        shopName: document.getElementById('shopName')?.value || 'مقهى البلايستيشن',
        shopAddress: document.getElementById('shopAddress')?.value || 'الرياض - المملكة العربية السعودية',
        shopPhone: document.getElementById('shopPhone')?.value || '+966 11 234 5678',
        shopEmail: document.getElementById('shopEmail')?.value || '<EMAIL>',
        footerMessage: document.getElementById('footerMessage')?.value || 'نسعد بخدمتكم',
        titleFontSize: document.getElementById('titleFontSize')?.value || 24,
        contentFontSize: document.getElementById('contentFontSize')?.value || 12,
        backgroundColor: document.getElementById('backgroundColor')?.value || '#ffffff',
        textColor: document.getElementById('textColor')?.value || '#000000',
        headerColor: document.getElementById('headerColor')?.value || '#1a1a2e'
    };
}

function generatePreviewHTML(design) {
    const sampleItems = [
        { name: 'قهوة عربية', price: 8.00, quantity: 1, subtotal: 8.00 },
        { name: 'شاي أحمر', price: 5.00, quantity: 2, subtotal: 10.00 },
        { name: 'عصير برتقال', price: 10.00, quantity: 1, subtotal: 10.00 }
    ];
    
    const total = sampleItems.reduce((sum, item) => sum + item.subtotal, 0);
    
    let html = `
        <div style="
            background: ${design.backgroundColor};
            color: ${design.textColor};
            font-size: ${design.contentFontSize}px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            direction: rtl;
            text-align: center;
        ">
            <h2 style="color: ${design.headerColor}; font-size: ${design.titleFontSize}px; margin-bottom: 10px;">
                ${design.shopName}
            </h2>
            <p style="margin-bottom: 5px;">${design.shopAddress}</p>
            <p style="margin-bottom: 5px;">${design.shopPhone}</p>
            <p style="margin-bottom: 15px;">${design.shopEmail}</p>
            
            <div style="border-top: 2px solid ${design.textColor}; border-bottom: 2px solid ${design.textColor}; padding: 10px 0; margin: 15px 0;">
                <p><strong>رقم الفاتورة:</strong> INV-20250108-001</p>
                <p><strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
            </div>
            
            <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                <thead>
                    <tr style="border-bottom: 1px solid ${design.textColor};">
                        <th style="padding: 5px; text-align: center;">الصنف</th>
                        <th style="padding: 5px; text-align: center;">السعر</th>
                        <th style="padding: 5px; text-align: center;">الكمية</th>
                        <th style="padding: 5px; text-align: center;">المجموع</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    sampleItems.forEach(item => {
        html += `
            <tr>
                <td style="padding: 3px; text-align: center;">${item.name}</td>
                <td style="padding: 3px; text-align: center;">${item.price.toFixed(2)}</td>
                <td style="padding: 3px; text-align: center;">${item.quantity}</td>
                <td style="padding: 3px; text-align: center;">${item.subtotal.toFixed(2)}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
            
            <div style="border-top: 2px solid ${design.textColor}; padding-top: 10px; margin-top: 15px;">
                <h3 style="color: ${design.headerColor};">المجموع الكلي: ${total.toFixed(2)} ريال</h3>
            </div>
            
            <div style="margin-top: 20px; font-style: italic;">
                ${design.footerMessage}
            </div>
        </div>
    `;
    
    return html;
}

function saveDesign() {
    const design = collectDesignSettings();
    
    // حفظ في التخزين المحلي
    localStorage.setItem('invoiceDesign', JSON.stringify(design));
    
    // إرسال للخادم
    sendToServer('save_design', design)
        .then(result => {
            if (result.success) {
                showNotification('تم حفظ التصميم بنجاح', 'success');
            } else {
                showNotification('فشل في حفظ التصميم', 'danger');
            }
        });
}

function exportTemplate() {
    const design = collectDesignSettings();
    const dataStr = JSON.stringify(design, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `invoice_template_${new Date().getTime()}.json`;
    link.click();
    
    showNotification('تم تصدير القالب بنجاح', 'success');
}

function resetDesign() {
    if (confirm('هل تريد إعادة تعيين جميع إعدادات التصميم؟')) {
        // إعادة تعيين القيم الافتراضية
        document.getElementById('paperType').value = 'thermal_80';
        document.getElementById('shopName').value = 'مقهى البلايستيشن';
        document.getElementById('shopAddress').value = 'الرياض - المملكة العربية السعودية';
        // ... باقي القيم
        
        updatePreview();
        showNotification('تم إعادة تعيين التصميم', 'info');
    }
}

// === وظائف إعدادات الطباعة ===

function loadPrinters() {
    fetch('/api/printers')
        .then(response => response.json())
        .then(data => {
            availablePrinters = data.printers || [];
            displayPrinters();
            updatePrinterSelect();
        })
        .catch(error => {
            console.error('خطأ في تحميل الطابعات:', error);
            // بيانات تجريبية
            availablePrinters = [
                { name: 'طابعة حرارية - USB', is_default: true, status: 'online' },
                { name: 'Microsoft Print to PDF', is_default: false, status: 'online' },
                { name: 'طابعة الشبكة', is_default: false, status: 'offline' }
            ];
            displayPrinters();
            updatePrinterSelect();
        });
}

function displayPrinters() {
    const printersList = document.getElementById('printersList');
    if (!printersList) return;
    
    printersList.innerHTML = '';
    
    availablePrinters.forEach((printer, index) => {
        const printerItem = document.createElement('div');
        printerItem.className = `printer-item ${printer.is_default ? 'default' : ''}`;
        printerItem.onclick = () => selectPrinter(index);
        
        printerItem.innerHTML = `
            <div>
                <strong>${printer.name}</strong>
                ${printer.is_default ? '<br><small>الطابعة الافتراضية</small>' : ''}
            </div>
            <div class="printer-status ${printer.status || 'online'}">
                ${printer.status === 'online' ? 'متصل' : 'غير متصل'}
            </div>
        `;
        
        printersList.appendChild(printerItem);
    });
}

function updatePrinterSelect() {
    const select = document.getElementById('defaultPrinter');
    if (!select) return;
    
    select.innerHTML = '<option value="">اختر طابعة...</option>';
    
    availablePrinters.forEach(printer => {
        const option = document.createElement('option');
        option.value = printer.name;
        option.textContent = printer.name;
        if (printer.is_default) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

function refreshPrinters() {
    showNotification('جاري تحديث قائمة الطابعات...', 'info');
    loadPrinters();
}

function selectPrinter(index) {
    document.querySelectorAll('.printer-item').forEach(item => {
        item.classList.remove('selected');
    });
    
    document.querySelectorAll('.printer-item')[index].classList.add('selected');
    
    const select = document.getElementById('defaultPrinter');
    if (select) {
        select.value = availablePrinters[index].name;
    }
}

function saveDefaultPrinter() {
    const settings = {
        defaultPrinter: document.getElementById('defaultPrinter')?.value,
        defaultReceiptType: document.getElementById('defaultReceiptType')?.value,
        autoPrint: document.getElementById('autoPrint')?.checked,
        printCopies: parseInt(document.getElementById('printCopies')?.value) || 1
    };
    
    sendToServer('save_printer_settings', settings)
        .then(result => {
            if (result.success) {
                showNotification('تم حفظ إعدادات الطباعة', 'success');
            } else {
                showNotification('فشل في حفظ الإعدادات', 'danger');
            }
        });
}

function selectReceiptType(type) {
    document.querySelectorAll('.receipt-type-card').forEach(card => {
        card.classList.remove('active');
    });
    
    document.querySelector(`[data-type="${type}"]`).classList.add('active');
    
    const select = document.getElementById('defaultReceiptType');
    if (select) {
        select.value = type;
    }
    
    showNotification(`تم اختيار نوع الفاتورة: ${getReceiptTypeName(type)}`, 'success');
}

function getReceiptTypeName(type) {
    const names = {
        'thermal_58': 'حراري 58 مم',
        'thermal_70': 'حراري 70 مم',
        'thermal_80': 'حراري 80 مم',
        'a4': 'A4 عادي'
    };
    return names[type] || type;
}

function runPrintTest() {
    const testType = document.getElementById('testType')?.value || 'simple';
    
    showNotification('جاري تشغيل اختبار الطباعة...', 'info');
    
    sendToServer('run_print_test', { type: testType })
        .then(result => {
            if (result.success) {
                showNotification('تم تشغيل الاختبار بنجاح', 'success');
                updatePrintLog(`اختبار ${testType} - نجح`);
            } else {
                showNotification('فشل في تشغيل الاختبار', 'danger');
                updatePrintLog(`اختبار ${testType} - فشل: ${result.error}`);
            }
        });
}

function printTestPage() {
    showNotification('جاري طباعة صفحة الاختبار...', 'info');
    
    sendToServer('print_test_page', {})
        .then(result => {
            if (result.success) {
                showNotification('تم إرسال صفحة الاختبار للطباعة', 'success');
                updatePrintLog('طباعة صفحة اختبار - نجح');
            } else {
                showNotification('فشل في طباعة صفحة الاختبار', 'danger');
                updatePrintLog(`طباعة صفحة اختبار - فشل: ${result.error}`);
            }
        });
}

function updatePrintLog(message) {
    const printLog = document.getElementById('printLog');
    if (!printLog) return;
    
    const timestamp = new Date().toLocaleTimeString('ar-SA');
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${timestamp}] ${message}`;
    
    printLog.appendChild(logEntry);
    printLog.scrollTop = printLog.scrollHeight;
}

// === وظائف مساعدة ===

function setupLayoutControls() {
    console.log('إعداد تحكم التخطيط');
}

function setupContentControls() {
    console.log('إعداد تحكم المحتوى');
}

function setupStyleControls() {
    console.log('إعداد تحكم الأنماط');
}

function setupPrintOptions() {
    console.log('إعداد خيارات الطباعة');
}

function setupReceiptTypes() {
    console.log('إعداد أنواع الفواتير');
}

function setupPrintTest() {
    console.log('إعداد اختبار الطباعة');
}

function setupPreviewEvents() {
    // إعداد أحداث المعاينة
    const previewScale = document.getElementById('previewScale');
    if (previewScale) {
        previewScale.addEventListener('input', updatePreview);
    }
}

function setupPrinterEvents() {
    // إعداد أحداث الطابعة
    const autoPrint = document.getElementById('autoPrint');
    if (autoPrint) {
        autoPrint.addEventListener('change', function() {
            printSettings.autoPrint = this.checked;
        });
    }
}

function loadPrinterSettings() {
    // تحميل إعدادات الطباعة المحفوظة
    const saved = localStorage.getItem('printerSettings');
    if (saved) {
        printSettings = JSON.parse(saved);
    }
}

function loadInvoiceDesign() {
    // تحميل تصميم الفاتورة المحفوظ
    const saved = localStorage.getItem('invoiceDesign');
    if (saved) {
        invoiceDesign = JSON.parse(saved);
    }
}

// تصدير الوظائف للاستخدام العام
window.showTab = showTab;
window.updatePreview = updatePreview;
window.saveDesign = saveDesign;
window.exportTemplate = exportTemplate;
window.resetDesign = resetDesign;
window.refreshPrinters = refreshPrinters;
window.saveDefaultPrinter = saveDefaultPrinter;
window.selectReceiptType = selectReceiptType;
window.runPrintTest = runPrintTest;
window.printTestPage = printTestPage;
