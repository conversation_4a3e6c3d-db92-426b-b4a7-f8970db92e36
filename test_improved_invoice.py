#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الفاتورة المحسنة مع التصميم الجديد
Test Improved Invoice with New Design

فكرة وتنفيذ: المهندس سيف رافع
هذا البرنامج مجاني لوجه الله تعالى
"""

import os
import json
import webbrowser
import threading
import time
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler

class ImprovedInvoiceHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path == '/':
            self.path = '/web_interface.html'
        elif self.path.startswith('/api/'):
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            if self.path == '/api/data':
                data = {
                    'items': [
                        {'id': 1, 'name': 'ساعة بلايستيشن', 'price': 15.0, 'category': 'ألعاب'},
                        {'id': 2, 'name': 'قهوة عربية', 'price': 8.0, 'category': 'مشروبات'},
                        {'id': 3, 'name': 'شاي أحمر', 'price': 5.0, 'category': 'مشروبات'},
                        {'id': 4, 'name': 'عصير برتقال', 'price': 10.0, 'category': 'مشروبات'},
                        {'id': 5, 'name': 'ساندويش تونة', 'price': 20.0, 'category': 'طعام'},
                        {'id': 6, 'name': 'بيتزا صغيرة', 'price': 35.0, 'category': 'طعام'},
                        {'id': 7, 'name': 'مياه معدنية', 'price': 2.0, 'category': 'مشروبات'},
                        {'id': 8, 'name': 'كولا', 'price': 6.0, 'category': 'مشروبات'},
                        {'id': 9, 'name': 'شيبس', 'price': 4.0, 'category': 'وجبات خفيفة'},
                        {'id': 10, 'name': 'شوكولاتة', 'price': 7.0, 'category': 'حلويات'}
                    ],
                    'stats': {
                        'today_invoices': 22,
                        'today_sales': 680.25,
                        'total_items': 10,
                        'avg_invoice': 30.92
                    },
                    'settings': {
                        'shop_name': 'مقهى البلايستيشن',
                        'shop_address': 'الرياض - المملكة العربية السعودية',
                        'shop_phone': '+966 11 234 5678',
                        'shop_email': '<EMAIL>',
                        'footer_message': 'نسعد بخدمتكم\nشكراً لزيارتكم'
                    }
                }
            elif self.path == '/api/printers':
                data = {
                    'printers': [
                        {'name': 'طابعة حرارية 58مم - USB', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة حرارية 80مم - USB', 'is_default': True, 'status': 'online'},
                        {'name': 'Microsoft Print to PDF', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة الشبكة HP LaserJet', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة Canon', 'is_default': False, 'status': 'offline'}
                    ]
                }
            else:
                data = {'success': True, 'message': 'تم تنفيذ العملية بنجاح'}
            
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            return
        
        return super().do_GET()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
                action = data.get('action', 'unknown')
                payload = data.get('data', {})
                
                # طباعة تفاصيل العملية
                if action == 'print_invoice':
                    print(f"🖨️ طباعة فاتورة محسنة:")
                    print(f"   • رقم الفاتورة: {payload.get('number', 'غير محدد')}")
                    print(f"   • المبلغ الإجمالي: {payload.get('total', 0)} ريال")
                    print(f"   • عدد الأصناف: {len(payload.get('items', []))}")
                    print(f"   • التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    
                elif action == 'save_design':
                    print(f"🎨 حفظ تصميم الفاتورة المحسن:")
                    print(f"   • حجم خط العنوان: {payload.get('titleFontSize', 'غير محدد')}px")
                    print(f"   • حجم خط المحتوى: {payload.get('contentFontSize', 'غير محدد')}px")
                    print(f"   • موضع الشعار: {payload.get('logoPosition', 'غير محدد')}")
                    print(f"   • لون الرأس: {payload.get('headerColor', 'غير محدد')}")
                    
                elif action == 'save_settings':
                    print(f"⚙️ حفظ إعدادات المحل")
                    if payload.get('shop_logo'):
                        print(f"   • تم حفظ شعار المحل")
                    
                else:
                    print(f"📝 إجراء: {action}")
                    
            except Exception as e:
                print(f"❌ خطأ في معالجة البيانات: {e}")
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = {'success': True, 'message': 'تم تنفيذ العملية بنجاح'}
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🎮 نظام نقاط البيع - الفاتورة المحسنة 🎮                  ║
    ║         POS System - Improved Invoice Design                ║
    ║                                                              ║
    ║    ✨ الإصدار الأول - النسخة الأولى من برنامج الفواتير ✨     ║
    ║                                                              ║
    ║    👨‍💻 فكرة وتنفيذ: المهندس سيف رافع                        ║
    ║    💝 هذا البرنامج مجاني لوجه الله تعالى                     ║
    ║                                                              ║
    ║    📅 التاريخ: {date}                                        ║
    ║    ⏰ الوقت: {time}                                          ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """.format(
        date=datetime.now().strftime("%Y-%m-%d"),
        time=datetime.now().strftime("%H:%M:%S")
    )
    print(banner)

def start_server(port=8080):
    """بدء الخادم"""
    try:
        server = HTTPServer(('localhost', port), ImprovedInvoiceHandler)
        print(f"🌐 الخادم يعمل على: http://localhost:{port}")
        
        # فتح المتصفح بعد ثانيتين
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print("⚠️ لم يتم فتح المتصفح تلقائياً")
                print(f"   يرجى فتح الرابط يدوياً: http://localhost:{port}")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n" + "="*80)
        print("🎮 نظام الفاتورة المحسنة جاهز للاختبار!")
        print("="*80)
        print("🆕 التحسينات الجديدة:")
        print("   • ✅ تحسين حجم خط المجموع الكلي ليكون متناسقاً")
        print("   • ✅ إضافة معلومات الفاتورة تحت بيانات المحل")
        print("   • ✅ عرض التاريخ والوقت بالتنسيق العربي")
        print("   • ✅ إضافة معلومات الكاشير وعدد الأصناف")
        print("   • ✅ تحسين تصميم الجدول والعناصر")
        print("   • ✅ إضافة قسم تفاصيل الطلب بشكل احترافي")
        print("="*80)
        print("🧪 اختبر الميزات التالية:")
        print("   1. أضف بعض الأصناف للفاتورة")
        print("   2. استخدم 'معاينة الفاتورة' لرؤية التحسينات")
        print("   3. لاحظ حجم خط المجموع الكلي المتناسق")
        print("   4. تحقق من معلومات الفاتورة تحت بيانات المحل")
        print("   5. جرب أحجام ورق مختلفة")
        print("   6. اختبر الطباعة الفعلية")
        print("="*80)
        print("💡 ملاحظات:")
        print("   • التاريخ والوقت يظهران بالتنسيق العربي")
        print("   • معلومات الكاشير وعدد الأصناف تظهر تلقائياً")
        print("   • التصميم متجاوب مع أحجام الورق المختلفة")
        print("   • جميع الخطوط متناسقة ومقروءة")
        print("="*80)
        print("⚠️ لإيقاف الخادم: اضغط Ctrl+C")
        print("="*80)
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 إيقاف الخادم...")
        server.shutdown()
        server.server_close()
        print("✅ تم إيقاف الخادم بنجاح")
        print("\n🙏 شكراً لاختبار الفاتورة المحسنة")
        print("💝 نسأل الله أن ينفع به ويجعله في ميزان حسناتنا")
        print("👨‍💻 فكرة وتنفيذ: المهندس سيف رافع")
        
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print("🔍 فحص الملفات المحدثة...")
    
    required_files = [
        'web_interface.html',
        'app.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع الملفات المطلوبة متوفرة")
    print("🚀 بدء اختبار الفاتورة المحسنة...")
    
    try:
        start_server()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
