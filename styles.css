/* 
نمط CSS لنظام نقاط البيع - مقهى البلايستيشن
CSS Styles for PlayStation Cafe POS System
*/

/* ========== متغيرات الألوان ========== */
:root {
    /* الألوان الأساسية */
    --primary-dark: #1a1a2e;
    --secondary-dark: #16213e;
    --accent-blue: #0f3460;
    --success-green: #00d4aa;
    --warning-orange: #ffb347;
    --danger-red: #ff6b6b;
    --info-cyan: #4ecdc4;
    --light-white: #ffffff;
    --dark-black: #0a0a0a;
    
    /* تدرجات الألوان */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
    --gradient-warning: linear-gradient(135deg, #ffb347 0%, #ff9500 100%);
    --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #e55656 100%);
    --gradient-info: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    
    /* ظلال */
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
    
    /* انتقالات */
    --transition-fast: all 0.2s ease;
    --transition-medium: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    
    /* خطوط */
    --font-primary: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
    --font-arabic: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    --font-mono: 'Courier New', 'Monaco', 'Consolas', monospace;
}

/* ========== الأنماط العامة ========== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-arabic);
    background: var(--primary-dark);
    color: var(--light-white);
    overflow-x: hidden;
    direction: rtl;
}

/* ========== الحاويات الرئيسية ========== */
.main-container {
    min-height: 100vh;
    background: var(--gradient-primary);
    position: relative;
}

.main-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

/* ========== الشريط العلوي ========== */
.header {
    background: var(--gradient-primary);
    padding: 20px;
    box-shadow: var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.header-title {
    font-size: 2.5rem;
    font-weight: bold;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.header-datetime {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 1rem;
    opacity: 0.9;
}

/* ========== الشريط الجانبي ========== */
.sidebar {
    background: var(--secondary-dark);
    width: 280px;
    height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    box-shadow: var(--shadow-heavy);
    transition: var(--transition-medium);
    z-index: 1000;
}

.sidebar-header {
    background: var(--accent-blue);
    padding: 20px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
}

.sidebar-menu {
    padding: 20px 0;
}

.menu-item {
    display: block;
    width: 100%;
    padding: 15px 25px;
    background: transparent;
    border: none;
    color: var(--light-white);
    font-size: 1rem;
    font-weight: 600;
    text-align: right;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-info);
    transition: var(--transition-medium);
    z-index: -1;
}

.menu-item:hover::before {
    right: 0;
}

.menu-item:hover {
    color: var(--light-white);
    transform: translateX(-5px);
}

.menu-item.active {
    background: var(--gradient-success);
    color: var(--light-white);
}

/* ========== البطاقات ========== */
.card {
    background: var(--secondary-dark);
    border-radius: 15px;
    padding: 20px;
    margin: 15px;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.card-header {
    background: var(--accent-blue);
    margin: -20px -20px 20px -20px;
    padding: 15px 20px;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 15px 15px 0 0;
}

/* ========== الأزرار ========== */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: var(--transition-medium);
    transform: translate(-50%, -50%);
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--light-white);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--light-white);
}

.btn-warning {
    background: var(--gradient-warning);
    color: var(--light-white);
}

.btn-danger {
    background: var(--gradient-danger);
    color: var(--light-white);
}

.btn-info {
    background: var(--gradient-info);
    color: var(--light-white);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn:active {
    transform: translateY(0);
}

/* ========== الجداول ========== */
.table-container {
    background: var(--light-white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    margin: 20px 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    color: var(--dark-black);
}

.table th {
    background: var(--gradient-primary);
    color: var(--light-white);
    padding: 15px;
    text-align: center;
    font-weight: bold;
}

.table td {
    padding: 12px 15px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.table tr:nth-child(even) {
    background: #f8f9fa;
}

.table tr:hover {
    background: #e3f2fd;
    transition: var(--transition-fast);
}

/* ========== حقول الإدخال ========== */
.input-group {
    margin: 15px 0;
}

.input-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--light-white);
}

.input-field {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid transparent;
    border-radius: 8px;
    font-size: 1rem;
    background: var(--light-white);
    color: var(--dark-black);
    transition: var(--transition-fast);
}

.input-field:focus {
    outline: none;
    border-color: var(--info-cyan);
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

/* ========== الإحصائيات ========== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background: var(--secondary-dark);
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: var(--transition-medium);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-success);
}

.stat-card:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--success-green);
    margin: 10px 0;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* ========== الحركات والتأثيرات ========== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
    50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.glow {
    animation: glow 2s infinite;
}

/* ========== التخطيط المتجاوب ========== */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .header-title {
        font-size: 1.8rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* ========== شريط التمرير المخصص ========== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--primary-dark);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-blue);
}

/* ========== النوافذ المنبثقة ========== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-medium);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--secondary-dark);
    border-radius: 15px;
    padding: 30px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    transform: scale(0.8);
    transition: var(--transition-medium);
}

.modal.show .modal-content {
    transform: scale(1);
}

/* ========== تأثيرات خاصة ========== */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.neon-text {
    text-shadow: 
        0 0 5px currentColor,
        0 0 10px currentColor,
        0 0 15px currentColor,
        0 0 20px currentColor;
}

.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* ========== طباعة الفواتير ========== */
.receipt-preview {
    background: white;
    color: black;
    font-family: var(--font-mono);
    font-size: 12px;
    line-height: 1.4;
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    max-height: 400px;
    overflow-y: auto;
}

.receipt-header {
    text-align: center;
    border-bottom: 2px solid #000;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.receipt-items {
    margin: 15px 0;
}

.receipt-total {
    border-top: 2px solid #000;
    padding-top: 10px;
    text-align: center;
    font-weight: bold;
}

/* ========== تحسينات إضافية ========== */
.loading-spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--info-cyan);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.success-message {
    background: var(--gradient-success);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    margin: 10px 0;
    animation: fadeIn 0.5s ease-out;
}

.error-message {
    background: var(--gradient-danger);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    margin: 10px 0;
    animation: fadeIn 0.5s ease-out;
}
