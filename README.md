# 🎮 نظام نقاط البيع - مقهى البلايستيشن
# PlayStation Cafe POS System

برنامج إصدار فواتير حرارية متكامل لمقاهي البلايستيشن مع واجهة عصرية وجذابة

A complete thermal receipt printing system for PlayStation cafes with modern and attractive interface

## 📋 الميزات الرئيسية | Main Features

### ✅ إصدار الفواتير الحرارية
- طباعة فواتير حرارية احترافية
- شعار المحل وبيانات الاتصال
- تفاصيل الأصناف والأسعار
- رسالة ختام مخصصة "نسعد بخدمتكم"

### ✅ إدارة الأصناف والأسعار
- إضافة وتعديل وحذف الأصناف
- تصنيف الأصناف (مشروبات، طعام، ألعاب، عام)
- إدارة الأسعار بسهولة
- بحث وتصفية الأصناف

### ✅ التقارير والإحصائيات
- تقارير يومية وأسبوعية وشهرية
- إحصائيات المبيعات والفواتير
- الأصناف الأكثر مبيعاً
- تصدير التقارير

### ✅ إعدادات المحل
- تخصيص اسم وعنوان المحل
- رفع شعار المحل
- تخصيص رسالة الختام
- معاينة الفاتورة

### ✅ واجهة عصرية
- تصميم عصري وجذاب
- ألوان متناسقة ومريحة للعين
- سهولة في الاستخدام
- دعم اللغة العربية

## 🛠️ المتطلبات | Requirements

### Python Requirements
```
Python 3.7+
tkinter (مدمج مع Python)
Pillow==10.0.0
reportlab==4.0.4
arabic-reshaper==3.0.0
python-bidi==0.4.2
pyinstaller==5.13.2 (للتحويل إلى exe)
```

### System Requirements
- Windows 10/11 (مُختبر)
- macOS 10.14+ (متوافق)
- Linux Ubuntu 18.04+ (متوافق)
- RAM: 512 MB أو أكثر
- مساحة القرص: 100 MB أو أكثر

## 🚀 التثبيت والتشغيل | Installation & Running

### الطريقة الأولى: تشغيل من الكود المصدري
```bash
# 1. تحميل المشروع
git clone [repository-url]
cd cafe-pos-system

# 2. تثبيت المكتبات المطلوبة
pip install -r requirements.txt

# أو تثبيت تلقائي
python run.py --install

# 3. تشغيل البرنامج
python run.py
```

### الطريقة الثانية: إنشاء ملف exe
```bash
# 1. بناء الملف التنفيذي
python build_exe.py

# 2. ستجد الملف في مجلد CafePOS_Distribution
# 3. قم بتشغيل CafePOS.exe
```

## 📁 هيكل المشروع | Project Structure

```
cafe-pos-system/
├── run.py                 # ملف التشغيل الرئيسي
├── main.py               # التطبيق الرئيسي
├── database.py           # إدارة قاعدة البيانات
├── invoice_generator.py  # مولد الفواتير
├── items_manager.py      # مدير الأصناف
├── settings_manager.py   # مدير الإعدادات
├── reports_manager.py    # مدير التقارير
├── thermal_printer.py    # نظام الطباعة الحرارية
├── build_exe.py          # أداة بناء ملف exe
├── requirements.txt      # المكتبات المطلوبة
├── README.md            # ملف التوثيق
├── logos/               # مجلد الشعارات
├── receipts/            # مجلد الفواتير المحفوظة
├── exports/             # مجلد التقارير المُصدرة
└── backups/             # مجلد النسخ الاحتياطية
```

## 🎯 كيفية الاستخدام | How to Use

### 1. التشغيل الأول
- عند التشغيل الأول، سيتم إنشاء قاعدة البيانات تلقائياً
- ستجد بعض الأصناف الافتراضية مُضافة مسبقاً

### 2. إضافة الأصناف
1. اختر "إدارة الأصناف" من القائمة الجانبية
2. انقر على "إضافة صنف جديد"
3. أدخل اسم الصنف والسعر والفئة
4. انقر "حفظ"

### 3. إصدار فاتورة
1. اختر "إصدار فاتورة جديدة"
2. اختر الأصناف من القائمة اليسرى
3. حدد الكمية وانقر "إضافة للفاتورة"
4. انقر "طباعة الفاتورة" أو "حفظ الفاتورة"

### 4. تخصيص الإعدادات
1. اختر "الإعدادات" من القائمة
2. أدخل اسم وعنوان المحل
3. ارفع شعار المحل (اختياري)
4. اكتب رسالة الختام
5. انقر "حفظ الإعدادات"

### 5. عرض التقارير
1. اختر "التقارير والإحصائيات"
2. اختر نوع التقرير (يومي، أسبوعي، شهري)
3. يمكنك تصدير التقرير كملف CSV

## 🔧 التخصيص | Customization

### تغيير الألوان
يمكنك تعديل الألوان في ملف `main.py` في دالة `setup_style()`:

```python
colors = {
    'bg': '#1a1a2e',           # لون الخلفية الرئيسي
    'fg': '#ffffff',           # لون النص
    'accent': '#16213e',       # لون الإطارات
    'button': '#0f3460',       # لون الأزرار
    'button_hover': '#e94560', # لون الأزرار عند التمرير
    'success': '#27ae60',      # لون النجاح
    'warning': '#f39c12',      # لون التحذير
    'danger': '#e74c3c'        # لون الخطر
}
```

### إضافة فئات جديدة
في ملف `items_manager.py`، يمكنك تعديل قائمة الفئات:

```python
category_combo = ttk.Combobox(fields_frame, textvariable=category_var,
                             values=['مشروبات', 'طعام', 'ألعاب', 'عام', 'فئة جديدة'],
                             font=('Arial', 12), width=27)
```

## 🐛 استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة:

#### 1. خطأ في المكتبات المفقودة
```bash
# الحل: تثبيت المكتبات
pip install -r requirements.txt
```

#### 2. خطأ في قاعدة البيانات
```bash
# الحل: حذف ملف قاعدة البيانات وإعادة التشغيل
rm cafe_pos.db
python run.py
```

#### 3. مشاكل في الطباعة
- تأكد من تثبيت طابعة افتراضية
- تحقق من إعدادات الطابعة في النظام

#### 4. مشاكل في عرض النص العربي
- تأكد من تثبيت `arabic-reshaper` و `python-bidi`
- تحقق من وجود خطوط عربية في النظام

## 📝 التطوير | Development

### إضافة ميزات جديدة
1. أنشئ ملف جديد للميزة (مثل `new_feature.py`)
2. أضف الاستيراد في `main.py`
3. أضف زر في الواجهة الرئيسية
4. اربط الزر بالوظيفة الجديدة

### اختبار البرنامج
```bash
# تشغيل الاختبارات (إذا كانت متوفرة)
python -m pytest tests/

# اختبار بناء exe
python build_exe.py
```

## 📄 الترخيص | License

هذا المشروع مطور لأغراض تعليمية وتجارية. يمكنك استخدامه وتعديله حسب احتياجاتك.

This project is developed for educational and commercial purposes. You can use and modify it according to your needs.

## 🤝 المساهمة | Contributing

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

We welcome contributions! Please:
1. Fork the project
2. Create a new feature branch
3. Add improvements
4. Submit a Pull Request

## 📞 الدعم | Support

للدعم الفني والاستفسارات:
- إنشاء Issue في GitHub
- التواصل مع المطور

For technical support and inquiries:
- Create an Issue on GitHub
- Contact the developer

## 🎉 شكر خاص | Special Thanks

شكر خاص لجميع المساهمين في تطوير هذا المشروع ولمجتمع Python مفتوح المصدر.

Special thanks to all contributors to this project and to the open source Python community.

---

**تم التطوير بواسطة: مساعد الذكي الاصطناعي**  
**Developed by: AI Assistant**

**تاريخ الإنشاء: 2025-01-08**  
**Creation Date: 2025-01-08**
