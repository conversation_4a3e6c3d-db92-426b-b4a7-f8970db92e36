import tkinter as tk
from tkinter import ttk, messagebox

class ItemsManager:
    def __init__(self, database):
        self.db = database
        self.items_window = None
        self.tree = None
        
    def open_items_window(self, parent):
        """فتح نافذة إدارة الأصناف"""
        # مسح المحتوى السابق
        for widget in parent.winfo_children():
            widget.destroy()
            
        # إنشاء الإطار الرئيسي
        main_frame = tk.Frame(parent, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(main_frame, text="📦 إدارة الأصناف والأسعار",
                              bg='#1a1a2e', fg='white',
                              font=('Arial', 20, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار العلوية
        buttons_frame = tk.Frame(main_frame, bg='#1a1a2e')
        buttons_frame.pack(fill='x', pady=(0, 20))
        
        # أزرار العمليات
        add_btn = tk.Button(buttons_frame, text="➕ إضافة صنف جديد",
                           command=self.add_item_dialog,
                           bg='#27ae60', fg='white',
                           font=('Arial', 12, 'bold'),
                           relief='flat', cursor='hand2',
                           width=20, height=2)
        add_btn.pack(side='left', padx=(0, 10))
        
        edit_btn = tk.Button(buttons_frame, text="✏️ تعديل الصنف",
                            command=self.edit_item_dialog,
                            bg='#3498db', fg='white',
                            font=('Arial', 12, 'bold'),
                            relief='flat', cursor='hand2',
                            width=20, height=2)
        edit_btn.pack(side='left', padx=10)
        
        delete_btn = tk.Button(buttons_frame, text="🗑️ حذف الصنف",
                              command=self.delete_item,
                              bg='#e74c3c', fg='white',
                              font=('Arial', 12, 'bold'),
                              relief='flat', cursor='hand2',
                              width=20, height=2)
        delete_btn.pack(side='left', padx=10)
        
        refresh_btn = tk.Button(buttons_frame, text="🔄 تحديث",
                               command=self.refresh_items,
                               bg='#9b59b6', fg='white',
                               font=('Arial', 12, 'bold'),
                               relief='flat', cursor='hand2',
                               width=15, height=2)
        refresh_btn.pack(side='right')
        
        # إطار الجدول
        table_frame = tk.Frame(main_frame, bg='#16213e')
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء الجدول
        self.create_items_table(table_frame)
        
        # تحميل البيانات
        self.refresh_items()
    
    def create_items_table(self, parent):
        """إنشاء جدول الأصناف"""
        # إطار الجدول مع شريط التمرير
        tree_frame = tk.Frame(parent, bg='#16213e')
        tree_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # تعريف الأعمدة
        columns = ('ID', 'اسم الصنف', 'السعر', 'الفئة', 'تاريخ الإضافة')
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # تخصيص الأعمدة
        self.tree.heading('ID', text='الرقم')
        self.tree.heading('اسم الصنف', text='اسم الصنف')
        self.tree.heading('السعر', text='السعر (ريال)')
        self.tree.heading('الفئة', text='الفئة')
        self.tree.heading('تاريخ الإضافة', text='تاريخ الإضافة')
        
        # تحديد عرض الأعمدة
        self.tree.column('ID', width=80, anchor='center')
        self.tree.column('اسم الصنف', width=200, anchor='center')
        self.tree.column('السعر', width=120, anchor='center')
        self.tree.column('الفئة', width=150, anchor='center')
        self.tree.column('تاريخ الإضافة', width=180, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج بالتعديل
        self.tree.bind('<Double-1>', lambda e: self.edit_item_dialog())
    
    def refresh_items(self):
        """تحديث قائمة الأصناف"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # تحميل البيانات من قاعدة البيانات
        items = self.db.get_all_items()
        
        for item in items:
            # تنسيق التاريخ
            date_str = item[4][:16] if item[4] else ''
            
            self.tree.insert('', 'end', values=(
                item[0],  # ID
                item[1],  # اسم الصنف
                f"{item[2]:.2f}",  # السعر
                item[3],  # الفئة
                date_str  # تاريخ الإضافة
            ))
    
    def add_item_dialog(self):
        """نافذة إضافة صنف جديد"""
        self.item_dialog("إضافة صنف جديد", "add")
    
    def edit_item_dialog(self):
        """نافذة تعديل صنف"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return
        
        item_data = self.tree.item(selected[0])['values']
        self.item_dialog("تعديل الصنف", "edit", item_data)
    
    def item_dialog(self, title, mode, item_data=None):
        """نافذة إضافة/تعديل الصنف"""
        dialog = tk.Toplevel()
        dialog.title(title)
        dialog.geometry("400x300")
        dialog.configure(bg='#1a1a2e')
        dialog.resizable(False, False)
        
        # جعل النافذة في المقدمة
        dialog.transient()
        dialog.grab_set()
        
        # العنوان
        title_label = tk.Label(dialog, text=title,
                              bg='#1a1a2e', fg='white',
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='#1a1a2e')
        fields_frame.pack(pady=20, padx=40, fill='x')
        
        # حقل اسم الصنف
        tk.Label(fields_frame, text="اسم الصنف:",
                bg='#1a1a2e', fg='white',
                font=('Arial', 12)).pack(anchor='w', pady=(0, 5))
        
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.pack(fill='x', pady=(0, 15))
        
        # حقل السعر
        tk.Label(fields_frame, text="السعر (ريال):",
                bg='#1a1a2e', fg='white',
                font=('Arial', 12)).pack(anchor='w', pady=(0, 5))
        
        price_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        price_entry.pack(fill='x', pady=(0, 15))
        
        # حقل الفئة
        tk.Label(fields_frame, text="الفئة:",
                bg='#1a1a2e', fg='white',
                font=('Arial', 12)).pack(anchor='w', pady=(0, 5))
        
        category_var = tk.StringVar()
        category_combo = ttk.Combobox(fields_frame, textvariable=category_var,
                                     values=['مشروبات', 'طعام', 'ألعاب', 'عام'],
                                     font=('Arial', 12), width=27)
        category_combo.pack(fill='x', pady=(0, 20))
        
        # ملء البيانات في حالة التعديل
        if mode == "edit" and item_data:
            name_entry.insert(0, item_data[1])
            price_entry.insert(0, str(item_data[2]))
            category_var.set(item_data[3])
        else:
            category_var.set('عام')
        
        # إطار الأزرار
        buttons_frame = tk.Frame(dialog, bg='#1a1a2e')
        buttons_frame.pack(pady=20)
        
        def save_item():
            name = name_entry.get().strip()
            price_str = price_entry.get().strip()
            category = category_var.get()
            
            # التحقق من صحة البيانات
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
                return
            
            try:
                price = float(price_str)
                if price <= 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
                return
            
            if not category:
                category = 'عام'
            
            try:
                if mode == "add":
                    self.db.add_item(name, price, category)
                    messagebox.showinfo("نجح", "تم إضافة الصنف بنجاح")
                else:  # edit
                    item_id = item_data[0]
                    self.db.update_item(item_id, name, price, category)
                    messagebox.showinfo("نجح", "تم تحديث الصنف بنجاح")
                
                self.refresh_items()
                dialog.destroy()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        
        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="💾 حفظ",
                            command=save_item,
                            bg='#27ae60', fg='white',
                            font=('Arial', 12, 'bold'),
                            relief='flat', cursor='hand2',
                            width=15, height=2)
        save_btn.pack(side='left', padx=10)
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                              command=dialog.destroy,
                              bg='#e74c3c', fg='white',
                              font=('Arial', 12, 'bold'),
                              relief='flat', cursor='hand2',
                              width=15, height=2)
        cancel_btn.pack(side='left', padx=10)
        
        # التركيز على حقل الاسم
        name_entry.focus()
    
    def delete_item(self):
        """حذف صنف"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return
        
        item_data = self.tree.item(selected[0])['values']
        item_name = item_data[1]
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل تريد حذف الصنف '{item_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                item_id = item_data[0]
                self.db.delete_item(item_id)
                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")
                self.refresh_items()
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
