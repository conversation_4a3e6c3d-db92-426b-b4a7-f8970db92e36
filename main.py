import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from datetime import datetime
from database import Database
from invoice_generator import InvoiceGenerator
from items_manager import ItemsManager
from settings_manager import SettingsManager
from reports_manager import ReportsManager

class CafePOSApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام نقاط البيع - مقهى البلايستيشن")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a2e')
        
        # تهيئة قاعدة البيانات
        self.db = Database()
        
        # تهيئة المولدات والمدراء
        self.invoice_generator = InvoiceGenerator(self.db)
        self.items_manager = ItemsManager(self.db)
        self.settings_manager = SettingsManager(self.db)
        self.reports_manager = ReportsManager(self.db)
        
        # إعداد الستايل
        self.setup_style()
        
        # إنشاء الواجهة الرئيسية
        self.create_main_interface()
        
        # تحديث الإحصائيات
        self.update_stats()
    
    def setup_style(self):
        """إعداد الستايل العصري للواجهة"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان عصرية
        colors = {
            'bg': '#1a1a2e',
            'fg': '#ffffff',
            'accent': '#16213e',
            'button': '#0f3460',
            'button_hover': '#e94560',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c'
        }
        
        # تخصيص الأزرار
        style.configure('Modern.TButton',
                       background=colors['button'],
                       foreground=colors['fg'],
                       borderwidth=0,
                       focuscolor='none',
                       font=('Arial', 12, 'bold'))
        
        style.map('Modern.TButton',
                 background=[('active', colors['button_hover'])])
        
        # تخصيص الإطارات
        style.configure('Modern.TFrame',
                       background=colors['bg'],
                       borderwidth=1,
                       relief='solid')
        
        # تخصيص التسميات
        style.configure('Modern.TLabel',
                       background=colors['bg'],
                       foreground=colors['fg'],
                       font=('Arial', 11))
        
        style.configure('Title.TLabel',
                       background=colors['bg'],
                       foreground=colors['fg'],
                       font=('Arial', 16, 'bold'))
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # الشريط العلوي
        self.create_header()
        
        # الشريط الجانبي
        self.create_sidebar()
        
        # المنطقة الرئيسية
        self.create_main_area()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(self.root, bg='#0f3460', height=80)
        header_frame.pack(fill='x', padx=10, pady=(10, 5))
        header_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = tk.Label(header_frame, 
                              text="🎮 نظام نقاط البيع - مقهى البلايستيشن",
                              bg='#0f3460', fg='white',
                              font=('Arial', 20, 'bold'))
        title_label.pack(side='left', padx=20, pady=20)
        
        # معلومات التاريخ والوقت
        self.datetime_label = tk.Label(header_frame,
                                      bg='#0f3460', fg='white',
                                      font=('Arial', 12))
        self.datetime_label.pack(side='right', padx=20, pady=20)
        self.update_datetime()
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = tk.Frame(self.root, bg='#16213e', width=250)
        sidebar_frame.pack(side='left', fill='y', padx=(10, 5), pady=5)
        sidebar_frame.pack_propagate(False)
        
        # عنوان القائمة
        menu_title = tk.Label(sidebar_frame, text="القائمة الرئيسية",
                             bg='#16213e', fg='white',
                             font=('Arial', 14, 'bold'))
        menu_title.pack(pady=20)
        
        # أزرار القائمة
        menu_buttons = [
            ("📄 إصدار فاتورة جديدة", self.open_new_invoice, '#27ae60'),
            ("📦 إدارة الأصناف", self.open_items_manager, '#3498db'),
            ("📊 التقارير والإحصائيات", self.open_reports, '#9b59b6'),
            ("⚙️ الإعدادات", self.open_settings, '#f39c12'),
            ("🚪 خروج", self.exit_app, '#e74c3c')
        ]
        
        for text, command, color in menu_buttons:
            btn = tk.Button(sidebar_frame, text=text,
                           command=command,
                           bg=color, fg='white',
                           font=('Arial', 12, 'bold'),
                           relief='flat',
                           cursor='hand2',
                           width=25, height=2)
            btn.pack(pady=10, padx=20, fill='x')
            
            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn: b.configure(bg='#e94560'))
            btn.bind("<Leave>", lambda e, b=btn, c=color: b.configure(bg=c))
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        self.main_frame = tk.Frame(self.root, bg='#1a1a2e')
        self.main_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        # لوحة الترحيب
        self.create_welcome_panel()
    
    def create_welcome_panel(self):
        """إنشاء لوحة الترحيب"""
        # مسح المحتوى السابق
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        welcome_frame = tk.Frame(self.main_frame, bg='#1a1a2e')
        welcome_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # رسالة الترحيب
        welcome_label = tk.Label(welcome_frame,
                                text="مرحباً بك في نظام نقاط البيع",
                                bg='#1a1a2e', fg='white',
                                font=('Arial', 24, 'bold'))
        welcome_label.pack(pady=50)
        
        # إحصائيات سريعة
        stats_frame = tk.Frame(welcome_frame, bg='#1a1a2e')
        stats_frame.pack(pady=30)
        
        # بطاقات الإحصائيات
        self.create_stats_cards(stats_frame)
        
        # آخر الفواتير
        self.create_recent_invoices_panel(welcome_frame)
    
    def create_stats_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        stats = self.db.get_daily_stats()
        invoice_count = stats[0] if stats[0] else 0
        total_sales = stats[1] if stats[1] else 0.0
        
        cards_data = [
            ("عدد الفواتير اليوم", str(invoice_count), '#3498db'),
            ("إجمالي المبيعات", f"{total_sales:.2f} ريال", '#27ae60'),
            ("متوسط الفاتورة", f"{(total_sales/invoice_count if invoice_count > 0 else 0):.2f} ريال", '#9b59b6')
        ]
        
        for i, (title, value, color) in enumerate(cards_data):
            card_frame = tk.Frame(parent, bg=color, width=200, height=120)
            card_frame.pack(side='left', padx=20, pady=10)
            card_frame.pack_propagate(False)
            
            title_label = tk.Label(card_frame, text=title,
                                  bg=color, fg='white',
                                  font=('Arial', 12, 'bold'))
            title_label.pack(pady=(20, 5))
            
            value_label = tk.Label(card_frame, text=value,
                                  bg=color, fg='white',
                                  font=('Arial', 16, 'bold'))
            value_label.pack()
    
    def create_recent_invoices_panel(self, parent):
        """إنشاء لوحة آخر الفواتير"""
        recent_frame = tk.Frame(parent, bg='#16213e')
        recent_frame.pack(fill='x', pady=30)
        
        title_label = tk.Label(recent_frame, text="آخر الفواتير",
                              bg='#16213e', fg='white',
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # جدول آخر الفواتير
        columns = ('رقم الفاتورة', 'المبلغ', 'التاريخ')
        tree = ttk.Treeview(recent_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=200, anchor='center')
        
        # إضافة البيانات
        recent_invoices = self.db.get_recent_invoices(10)
        for invoice in recent_invoices:
            tree.insert('', 'end', values=(
                invoice[0],  # رقم الفاتورة
                f"{invoice[1]:.2f} ريال",  # المبلغ
                invoice[2][:16]  # التاريخ
            ))
        
        tree.pack(pady=10, padx=20, fill='x')
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.root, bg='#0f3460', height=30)
        status_frame.pack(fill='x', side='bottom', padx=10, pady=(5, 10))
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="جاهز",
                                    bg='#0f3460', fg='white',
                                    font=('Arial', 10))
        self.status_label.pack(side='left', padx=10, pady=5)
    
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.now()
        datetime_str = now.strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.config(text=datetime_str)
        self.root.after(1000, self.update_datetime)
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        self.create_welcome_panel()
        self.root.after(60000, self.update_stats)  # تحديث كل دقيقة
    
    def open_new_invoice(self):
        """فتح نافذة إصدار فاتورة جديدة"""
        self.invoice_generator.open_invoice_window(self.main_frame)
        self.status_label.config(text="إصدار فاتورة جديدة")
    
    def open_items_manager(self):
        """فتح نافذة إدارة الأصناف"""
        self.items_manager.open_items_window(self.main_frame)
        self.status_label.config(text="إدارة الأصناف")
    
    def open_reports(self):
        """فتح نافذة التقارير"""
        self.reports_manager.open_reports_window(self.main_frame)
        self.status_label.config(text="التقارير والإحصائيات")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        self.settings_manager.open_settings_window(self.main_frame)
        self.status_label.config(text="الإعدادات")
    
    def exit_app(self):
        """خروج من التطبيق"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد الخروج من البرنامج؟"):
            self.root.quit()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CafePOSApp()
    app.run()
