import os
import tempfile
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.pdfgen import canvas
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display
from PIL import Image, ImageDraw, ImageFont
import tkinter as tk
from tkinter import messagebox
import subprocess
import sys

class ThermalPrinter:
    def __init__(self, database):
        self.db = database
        self.receipt_width = 80  # عرض الإيصال بالمليمتر
        self.font_size = 12
        
    def print_invoice(self, invoice_number, items, total_amount):
        """طباعة الفاتورة الحرارية"""
        try:
            # إنشاء ملف PDF للفاتورة
            pdf_path = self.create_thermal_receipt_pdf(invoice_number, items, total_amount)
            
            # فتح الملف للطباعة
            self.open_for_printing(pdf_path)
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء الفاتورة: {str(e)}")
    
    def create_thermal_receipt_pdf(self, invoice_number, items, total_amount):
        """إنشاء ملف PDF للإيصال الحراري"""
        # إنشاء ملف مؤقت
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        pdf_path = temp_file.name
        temp_file.close()
        
        # إعداد الصفحة (حجم الإيصال الحراري)
        page_width = self.receipt_width * mm
        page_height = 200 * mm  # ارتفاع متغير حسب المحتوى
        
        # إنشاء الـ PDF
        c = canvas.Canvas(pdf_path, pagesize=(page_width, page_height))
        
        # الحصول على إعدادات المحل
        settings = self.db.get_settings()
        shop_name = settings[1] if settings else "مقهى البلايستيشن"
        shop_address = settings[2] if settings else "الرياض - المملكة العربية السعودية"
        footer_message = settings[4] if settings else "نسعد بخدمتكم"
        logo_path = settings[3] if settings and settings[3] else None
        
        y_position = page_height - 20 * mm
        
        # رسم الشعار إذا كان متوفراً
        if logo_path and os.path.exists(logo_path):
            try:
                # رسم الشعار
                logo_size = 15 * mm
                logo_x = (page_width - logo_size) / 2
                c.drawImage(logo_path, logo_x, y_position - logo_size, 
                           width=logo_size, height=logo_size)
                y_position -= logo_size + 5 * mm
            except:
                pass  # تجاهل الأخطاء في الشعار
        
        # اسم المحل
        c.setFont("Helvetica-Bold", 14)
        text_width = c.stringWidth(shop_name, "Helvetica-Bold", 14)
        c.drawString((page_width - text_width) / 2, y_position, shop_name)
        y_position -= 6 * mm
        
        # عنوان المحل
        c.setFont("Helvetica", 10)
        text_width = c.stringWidth(shop_address, "Helvetica", 10)
        c.drawString((page_width - text_width) / 2, y_position, shop_address)
        y_position -= 8 * mm
        
        # خط فاصل
        c.line(5 * mm, y_position, page_width - 5 * mm, y_position)
        y_position -= 5 * mm
        
        # معلومات الفاتورة
        c.setFont("Helvetica-Bold", 10)
        invoice_info = f"Invoice: {invoice_number}"
        c.drawString(5 * mm, y_position, invoice_info)
        y_position -= 4 * mm
        
        date_info = f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        c.drawString(5 * mm, y_position, date_info)
        y_position -= 6 * mm
        
        # خط فاصل
        c.line(5 * mm, y_position, page_width - 5 * mm, y_position)
        y_position -= 5 * mm
        
        # عناوين الجدول
        c.setFont("Helvetica-Bold", 9)
        c.drawString(5 * mm, y_position, "Item")
        c.drawString(35 * mm, y_position, "Qty")
        c.drawString(45 * mm, y_position, "Price")
        c.drawString(60 * mm, y_position, "Total")
        y_position -= 4 * mm
        
        # خط تحت العناوين
        c.line(5 * mm, y_position, page_width - 5 * mm, y_position)
        y_position -= 3 * mm
        
        # أصناف الفاتورة
        c.setFont("Helvetica", 8)
        for item in items:
            # اسم الصنف (مع تقصير النص إذا كان طويلاً)
            item_name = item['name']
            if len(item_name) > 20:
                item_name = item_name[:17] + "..."
            
            c.drawString(5 * mm, y_position, item_name)
            c.drawString(35 * mm, y_position, str(item['quantity']))
            c.drawString(45 * mm, y_position, f"{item['price']:.2f}")
            c.drawString(60 * mm, y_position, f"{item['subtotal']:.2f}")
            y_position -= 4 * mm
        
        # خط فاصل قبل المجموع
        y_position -= 2 * mm
        c.line(5 * mm, y_position, page_width - 5 * mm, y_position)
        y_position -= 5 * mm
        
        # المجموع الكلي
        c.setFont("Helvetica-Bold", 12)
        total_text = f"Total: {total_amount:.2f} SAR"
        text_width = c.stringWidth(total_text, "Helvetica-Bold", 12)
        c.drawString((page_width - text_width) / 2, y_position, total_text)
        y_position -= 8 * mm
        
        # خط فاصل
        c.line(5 * mm, y_position, page_width - 5 * mm, y_position)
        y_position -= 5 * mm
        
        # رسالة الختام
        c.setFont("Helvetica", 10)
        text_width = c.stringWidth(footer_message, "Helvetica", 10)
        c.drawString((page_width - text_width) / 2, y_position, footer_message)
        y_position -= 6 * mm
        
        # معلومات إضافية
        c.setFont("Helvetica", 8)
        thank_you = "Thank you for your visit!"
        text_width = c.stringWidth(thank_you, "Helvetica", 8)
        c.drawString((page_width - text_width) / 2, y_position, thank_you)
        
        # حفظ الـ PDF
        c.save()
        
        return pdf_path
    
    def create_text_receipt(self, invoice_number, items, total_amount):
        """إنشاء إيصال نصي للطباعة المباشرة على الطابعة الحرارية"""
        # الحصول على إعدادات المحل
        settings = self.db.get_settings()
        shop_name = settings[1] if settings else "مقهى البلايستيشن"
        shop_address = settings[2] if settings else "الرياض - المملكة العربية السعودية"
        footer_message = settings[4] if settings else "نسعد بخدمتكم"
        
        # بناء النص
        receipt_text = []
        
        # رأس الإيصال
        receipt_text.append("=" * 32)
        receipt_text.append(shop_name.center(32))
        receipt_text.append(shop_address.center(32))
        receipt_text.append("=" * 32)
        receipt_text.append("")
        
        # معلومات الفاتورة
        receipt_text.append(f"Invoice: {invoice_number}")
        receipt_text.append(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        receipt_text.append("-" * 32)
        
        # عناوين الجدول
        receipt_text.append("Item            Qty  Price  Total")
        receipt_text.append("-" * 32)
        
        # الأصناف
        for item in items:
            name = item['name'][:15].ljust(15)
            qty = str(item['quantity']).rjust(3)
            price = f"{item['price']:.2f}".rjust(6)
            subtotal = f"{item['subtotal']:.2f}".rjust(6)
            receipt_text.append(f"{name} {qty} {price} {subtotal}")
        
        # المجموع
        receipt_text.append("-" * 32)
        total_line = f"TOTAL: {total_amount:.2f} SAR"
        receipt_text.append(total_line.center(32))
        receipt_text.append("=" * 32)
        receipt_text.append("")
        receipt_text.append(footer_message.center(32))
        receipt_text.append("Thank you for your visit!".center(32))
        receipt_text.append("")
        receipt_text.append("")
        receipt_text.append("")  # مساحة للقطع
        
        return "\n".join(receipt_text)
    
    def open_for_printing(self, pdf_path):
        """فتح الملف للطباعة"""
        try:
            if sys.platform.startswith('win'):
                # Windows
                os.startfile(pdf_path, "print")
            elif sys.platform.startswith('darwin'):
                # macOS
                subprocess.run(["open", pdf_path])
            else:
                # Linux
                subprocess.run(["xdg-open", pdf_path])
                
        except Exception as e:
            # في حالة فشل الطباعة المباشرة، فتح الملف للعرض
            try:
                if sys.platform.startswith('win'):
                    os.startfile(pdf_path)
                elif sys.platform.startswith('darwin'):
                    subprocess.run(["open", pdf_path])
                else:
                    subprocess.run(["xdg-open", pdf_path])
            except:
                raise Exception("لا يمكن فتح الملف للطباعة")
    
    def save_receipt_as_image(self, invoice_number, items, total_amount):
        """حفظ الإيصال كصورة"""
        try:
            # إنشاء صورة للإيصال
            img_width = 300
            img_height = 400 + (len(items) * 20)
            
            img = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(img)
            
            # محاولة تحميل خط عربي
            try:
                font_large = ImageFont.truetype("arial.ttf", 16)
                font_medium = ImageFont.truetype("arial.ttf", 12)
                font_small = ImageFont.truetype("arial.ttf", 10)
            except:
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            # الحصول على إعدادات المحل
            settings = self.db.get_settings()
            shop_name = settings[1] if settings else "مقهى البلايستيشن"
            shop_address = settings[2] if settings else "الرياض - المملكة العربية السعودية"
            footer_message = settings[4] if settings else "نسعد بخدمتكم"
            
            y = 20
            
            # اسم المحل
            text_width = draw.textlength(shop_name, font=font_large)
            x = (img_width - text_width) // 2
            draw.text((x, y), shop_name, fill='black', font=font_large)
            y += 30
            
            # العنوان
            text_width = draw.textlength(shop_address, font=font_small)
            x = (img_width - text_width) // 2
            draw.text((x, y), shop_address, fill='black', font=font_small)
            y += 25
            
            # خط فاصل
            draw.line([(20, y), (img_width-20, y)], fill='black', width=1)
            y += 15
            
            # معلومات الفاتورة
            draw.text((20, y), f"Invoice: {invoice_number}", fill='black', font=font_medium)
            y += 20
            draw.text((20, y), f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}", fill='black', font=font_medium)
            y += 25
            
            # خط فاصل
            draw.line([(20, y), (img_width-20, y)], fill='black', width=1)
            y += 15
            
            # عناوين الجدول
            draw.text((20, y), "Item", fill='black', font=font_medium)
            draw.text((150, y), "Qty", fill='black', font=font_medium)
            draw.text((180, y), "Price", fill='black', font=font_medium)
            draw.text((230, y), "Total", fill='black', font=font_medium)
            y += 20
            
            # الأصناف
            for item in items:
                item_name = item['name'][:15] + "..." if len(item['name']) > 15 else item['name']
                draw.text((20, y), item_name, fill='black', font=font_small)
                draw.text((150, y), str(item['quantity']), fill='black', font=font_small)
                draw.text((180, y), f"{item['price']:.2f}", fill='black', font=font_small)
                draw.text((230, y), f"{item['subtotal']:.2f}", fill='black', font=font_small)
                y += 18
            
            # خط فاصل
            y += 10
            draw.line([(20, y), (img_width-20, y)], fill='black', width=2)
            y += 20
            
            # المجموع
            total_text = f"TOTAL: {total_amount:.2f} SAR"
            text_width = draw.textlength(total_text, font=font_large)
            x = (img_width - text_width) // 2
            draw.text((x, y), total_text, fill='black', font=font_large)
            y += 40
            
            # رسالة الختام
            text_width = draw.textlength(footer_message, font=font_medium)
            x = (img_width - text_width) // 2
            draw.text((x, y), footer_message, fill='black', font=font_medium)
            
            # حفظ الصورة
            img_path = f"receipt_{invoice_number}.png"
            img.save(img_path)
            
            return img_path
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء صورة الإيصال: {str(e)}")
    
    def print_text_to_thermal_printer(self, text_content, printer_name=None):
        """طباعة نص مباشرة على الطابعة الحرارية"""
        try:
            # إنشاء ملف نصي مؤقت
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8')
            temp_file.write(text_content)
            temp_file.close()
            
            # طباعة الملف
            if sys.platform.startswith('win'):
                if printer_name:
                    subprocess.run(["print", f"/D:{printer_name}", temp_file.name], shell=True)
                else:
                    subprocess.run(["print", temp_file.name], shell=True)
            else:
                # Linux/Mac
                if printer_name:
                    subprocess.run(["lp", "-d", printer_name, temp_file.name])
                else:
                    subprocess.run(["lp", temp_file.name])
            
            # حذف الملف المؤقت
            os.unlink(temp_file.name)
            
        except Exception as e:
            raise Exception(f"خطأ في الطباعة: {str(e)}")
