#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقاط البيع - مقهى البلايستيشن
Point of Sale System - PlayStation Cafe

تطوير: مساعد الذكي الاصطناعي
Development: AI Assistant

الوصف: برنامج إصدار فواتير حرارية لمقهى البلايستيشن
Description: Thermal receipt printing system for PlayStation Cafe
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_modules = [
        'tkinter',
        'sqlite3',
        'PIL',
        'reportlab',
        'arabic_reshaper',
        'bidi'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'PIL':
                import PIL
            elif module == 'bidi':
                import bidi
            elif module == 'arabic_reshaper':
                import arabic_reshaper
            elif module == 'reportlab':
                import reportlab
            elif module == 'tkinter':
                import tkinter
            elif module == 'sqlite3':
                import sqlite3
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        error_msg = f"""
المكتبات التالية مفقودة:
Missing required modules:

{', '.join(missing_modules)}

يرجى تثبيتها باستخدام:
Please install them using:

pip install {' '.join(missing_modules)}
        """
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في المكتبات - Missing Dependencies", error_msg)
        return False
    
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['logos', 'receipts', 'exports', 'backups']
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
            except Exception as e:
                print(f"تحذير: لا يمكن إنشاء المجلد {directory}: {e}")

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # فحص المكتبات المطلوبة
        if not check_dependencies():
            return
        
        # إنشاء المجلدات المطلوبة
        create_directories()
        
        # استيراد التطبيق الرئيسي
        from main import CafePOSApp
        
        # إنشاء وتشغيل التطبيق
        app = CafePOSApp()
        
        # إعداد معالج الأخطاء
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            print(f"خطأ غير متوقع: {error_msg}")
            
            # عرض رسالة خطأ للمستخدم
            messagebox.showerror("خطأ غير متوقع", 
                               f"حدث خطأ غير متوقع في البرنامج:\n\n{str(exc_value)}\n\nيرجى إعادة تشغيل البرنامج.")
        
        sys.excepthook = handle_exception
        
        # تشغيل التطبيق
        print("🎮 بدء تشغيل نظام نقاط البيع - مقهى البلايستيشن")
        print("Starting PlayStation Cafe POS System...")
        
        app.run()
        
    except ImportError as e:
        error_msg = f"""
خطأ في استيراد الوحدات:
Import Error:

{str(e)}

تأكد من وجود جميع الملفات المطلوبة:
- main.py
- database.py
- invoice_generator.py
- items_manager.py
- settings_manager.py
- reports_manager.py
- thermal_printer.py
        """
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في الاستيراد - Import Error", error_msg)
        
    except Exception as e:
        error_msg = f"""
خطأ في تشغيل البرنامج:
Application Error:

{str(e)}

{traceback.format_exc()}
        """
        
        print(error_msg)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التطبيق - Application Error", str(e))
        except:
            pass

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    import subprocess
    
    requirements = [
        'Pillow==10.0.0',
        'reportlab==4.0.4',
        'arabic-reshaper==3.0.0',
        'python-bidi==0.4.2'
    ]
    
    print("تثبيت المكتبات المطلوبة...")
    print("Installing required packages...")
    
    for package in requirements:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ تم تثبيت {package} بنجاح")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت {package}: {e}")
            return False
    
    print("✅ تم تثبيت جميع المكتبات بنجاح!")
    return True

if __name__ == "__main__":
    # التحقق من وسائط سطر الأوامر
    if len(sys.argv) > 1:
        if sys.argv[1] == "--install":
            install_requirements()
        elif sys.argv[1] == "--help":
            print("""
نظام نقاط البيع - مقهى البلايستيشن
PlayStation Cafe POS System

الاستخدام:
Usage:

python run.py              - تشغيل البرنامج (Run the application)
python run.py --install    - تثبيت المكتبات المطلوبة (Install requirements)
python run.py --help       - عرض هذه المساعدة (Show this help)

الميزات:
Features:

✅ إصدار فواتير حرارية (Thermal receipt printing)
✅ إدارة الأصناف والأسعار (Items and prices management)
✅ التقارير والإحصائيات (Reports and statistics)
✅ إعدادات المحل (Shop settings)
✅ واجهة عصرية وجذابة (Modern and attractive interface)
✅ قابلية التحويل إلى exe (Executable conversion ready)

المتطلبات:
Requirements:

- Python 3.7+
- tkinter (مدمج مع Python)
- Pillow (PIL)
- reportlab
- arabic-reshaper
- python-bidi

للدعم والمساعدة:
For support and help:

يرجى التواصل مع المطور
Please contact the developer
            """)
        else:
            print(f"خيار غير معروف: {sys.argv[1]}")
            print(f"Unknown option: {sys.argv[1]}")
            print("استخدم --help للمساعدة")
            print("Use --help for help")
    else:
        main()
