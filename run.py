#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقاط البيع - مقهى البلايستيشن
Point of Sale System - PlayStation Cafe

تطوير: مساعد الذكي الاصطناعي
Development: AI Assistant

الوصف: برنامج إصدار فواتير حرارية لمقهى البلايستيشن
Description: Thermal receipt printing system for PlayStation Cafe
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_modules = {
        'tkinter': 'tkinter',
        'sqlite3': 'sqlite3',
        'PIL': 'Pillow',
        'reportlab': 'reportlab',
        'arabic_reshaper': 'arabic-reshaper',
        'bidi': 'python-bidi'
    }

    missing_modules = []

    for module, pip_name in required_modules.items():
        try:
            if module == 'PIL':
                from PIL import Image
            elif module == 'bidi':
                from bidi.algorithm import get_display
            elif module == 'arabic_reshaper':
                import arabic_reshaper
            elif module == 'reportlab':
                from reportlab.pdfgen import canvas
            elif module == 'tkinter':
                import tkinter as tk
            elif module == 'sqlite3':
                import sqlite3
        except ImportError:
            missing_modules.append(pip_name)

    if missing_modules:
        print("=" * 60)
        print("🚨 المكتبات التالية مفقودة | Missing required modules:")
        print("=" * 60)
        for module in missing_modules:
            print(f"❌ {module}")

        print("\n💡 لتثبيت المكتبات المطلوبة:")
        print("To install required modules:")
        print(f"pip install {' '.join(missing_modules)}")

        print("\n🔄 أو استخدم الأمر التالي:")
        print("Or use the following command:")
        print("python run.py --install")

        input("\n⏸️ اضغط Enter للخروج | Press Enter to exit...")
        return False

    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['logos', 'receipts', 'exports', 'backups']
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
            except Exception as e:
                print(f"تحذير: لا يمكن إنشاء المجلد {directory}: {e}")

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # فحص المكتبات المطلوبة
        if not check_dependencies():
            return
        
        # إنشاء المجلدات المطلوبة
        create_directories()
        
        # استيراد التطبيق الرئيسي
        from main import CafePOSApp
        
        # إنشاء وتشغيل التطبيق
        app = CafePOSApp()
        
        # إعداد معالج الأخطاء
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            print(f"خطأ غير متوقع: {error_msg}")
            
            # عرض رسالة خطأ للمستخدم
            messagebox.showerror("خطأ غير متوقع", 
                               f"حدث خطأ غير متوقع في البرنامج:\n\n{str(exc_value)}\n\nيرجى إعادة تشغيل البرنامج.")
        
        sys.excepthook = handle_exception
        
        # تشغيل التطبيق
        print("🎮 بدء تشغيل نظام نقاط البيع - مقهى البلايستيشن")
        print("Starting PlayStation Cafe POS System...")
        
        app.run()
        
    except ImportError as e:
        error_msg = f"""
خطأ في استيراد الوحدات:
Import Error:

{str(e)}

تأكد من وجود جميع الملفات المطلوبة:
- main.py
- database.py
- invoice_generator.py
- items_manager.py
- settings_manager.py
- reports_manager.py
- thermal_printer.py
        """
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في الاستيراد - Import Error", error_msg)
        
    except Exception as e:
        error_msg = f"""
خطأ في تشغيل البرنامج:
Application Error:

{str(e)}

{traceback.format_exc()}
        """
        
        print(error_msg)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التطبيق - Application Error", str(e))
        except:
            pass

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    import subprocess

    requirements = [
        'Pillow',
        'reportlab',
        'arabic-reshaper',
        'python-bidi'
    ]

    print("🔄 تثبيت المكتبات المطلوبة...")
    print("Installing required packages...")
    print("=" * 50)

    success_count = 0

    for package in requirements:
        try:
            print(f"📦 تثبيت {package}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', package],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                print(f"✅ تم تثبيت {package} بنجاح")
                success_count += 1
            else:
                print(f"❌ فشل في تثبيت {package}")
                print(f"خطأ: {result.stderr}")

        except Exception as e:
            print(f"❌ خطأ في تثبيت {package}: {e}")

    print("=" * 50)
    if success_count == len(requirements):
        print("🎉 تم تثبيت جميع المكتبات بنجاح!")
        print("يمكنك الآن تشغيل البرنامج باستخدام: python run.py")
        return True
    else:
        print(f"⚠️ تم تثبيت {success_count} من {len(requirements)} مكتبات")
        print("يرجى المحاولة مرة أخرى أو تثبيت المكتبات يدوياً")
        return False

if __name__ == "__main__":
    # التحقق من وسائط سطر الأوامر
    if len(sys.argv) > 1:
        if sys.argv[1] == "--install":
            install_requirements()
        elif sys.argv[1] == "--help":
            print("""
نظام نقاط البيع - مقهى البلايستيشن
PlayStation Cafe POS System

الاستخدام:
Usage:

python run.py              - تشغيل البرنامج (Run the application)
python run.py --install    - تثبيت المكتبات المطلوبة (Install requirements)
python run.py --help       - عرض هذه المساعدة (Show this help)

الميزات:
Features:

✅ إصدار فواتير حرارية (Thermal receipt printing)
✅ إدارة الأصناف والأسعار (Items and prices management)
✅ التقارير والإحصائيات (Reports and statistics)
✅ إعدادات المحل (Shop settings)
✅ واجهة عصرية وجذابة (Modern and attractive interface)
✅ قابلية التحويل إلى exe (Executable conversion ready)

المتطلبات:
Requirements:

- Python 3.7+
- tkinter (مدمج مع Python)
- Pillow (PIL)
- reportlab
- arabic-reshaper
- python-bidi

للدعم والمساعدة:
For support and help:

يرجى التواصل مع المطور
Please contact the developer
            """)
        else:
            print(f"خيار غير معروف: {sys.argv[1]}")
            print(f"Unknown option: {sys.argv[1]}")
            print("استخدم --help للمساعدة")
            print("Use --help for help")
    else:
        main()
