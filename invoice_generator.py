import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import random
from thermal_printer import ThermalPrinter

class InvoiceGenerator:
    def __init__(self, database):
        self.db = database
        self.invoice_items = []
        self.total_amount = 0.0
        self.thermal_printer = ThermalPrinter(database)
        
    def open_invoice_window(self, parent):
        """فتح نافذة إصدار فاتورة جديدة"""
        # مسح المحتوى السابق
        for widget in parent.winfo_children():
            widget.destroy()
            
        # إنشاء الإطار الرئيسي
        main_frame = tk.Frame(parent, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(main_frame, text="📄 إصدار فاتورة جديدة",
                              bg='#1a1a2e', fg='white',
                              font=('Arial', 20, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg='#1a1a2e')
        content_frame.pack(fill='both', expand=True)
        
        # الجانب الأيسر - اختيار الأصناف
        self.create_items_selection(content_frame)
        
        # الجانب الأيمن - تفاصيل الفاتورة
        self.create_invoice_details(content_frame)
        
        # إطار الأزرار السفلي
        self.create_invoice_buttons(main_frame)
        
        # تحديث قائمة الأصناف
        self.refresh_available_items()
        
        # إعادة تعيين الفاتورة
        self.reset_invoice()
    
    def create_items_selection(self, parent):
        """إنشاء قسم اختيار الأصناف"""
        # الإطار الأيسر
        left_frame = tk.Frame(parent, bg='#16213e', width=400)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # عنوان القسم
        title_label = tk.Label(left_frame, text="الأصناف المتاحة",
                              bg='#16213e', fg='white',
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=15)
        
        # شريط البحث
        search_frame = tk.Frame(left_frame, bg='#16213e')
        search_frame.pack(fill='x', padx=20, pady=(0, 15))
        
        tk.Label(search_frame, text="🔍 البحث:",
                bg='#16213e', fg='white',
                font=('Arial', 12)).pack(side='left')
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=('Arial', 12), width=25)
        search_entry.pack(side='right', fill='x', expand=True, padx=(10, 0))
        search_entry.bind('<KeyRelease>', self.filter_items)
        
        # قائمة الأصناف
        items_frame = tk.Frame(left_frame, bg='#16213e')
        items_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # إنشاء Listbox للأصناف
        self.items_listbox = tk.Listbox(items_frame,
                                       font=('Arial', 11),
                                       bg='white', fg='black',
                                       selectmode='single',
                                       height=15)
        self.items_listbox.pack(side='left', fill='both', expand=True)
        
        # شريط التمرير للقائمة
        items_scrollbar = ttk.Scrollbar(items_frame, orient='vertical',
                                       command=self.items_listbox.yview)
        self.items_listbox.configure(yscrollcommand=items_scrollbar.set)
        items_scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج بإضافة الصنف
        self.items_listbox.bind('<Double-1>', self.add_item_to_invoice)
        
        # إطار إضافة الصنف
        add_frame = tk.Frame(left_frame, bg='#16213e')
        add_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        tk.Label(add_frame, text="الكمية:",
                bg='#16213e', fg='white',
                font=('Arial', 12)).pack(side='left')
        
        self.quantity_var = tk.StringVar(value="1")
        quantity_entry = tk.Entry(add_frame, textvariable=self.quantity_var,
                                 font=('Arial', 12), width=5)
        quantity_entry.pack(side='left', padx=(10, 10))
        
        add_btn = tk.Button(add_frame, text="➕ إضافة للفاتورة",
                           command=self.add_item_to_invoice,
                           bg='#27ae60', fg='white',
                           font=('Arial', 12, 'bold'),
                           relief='flat', cursor='hand2')
        add_btn.pack(side='right')
    
    def create_invoice_details(self, parent):
        """إنشاء قسم تفاصيل الفاتورة"""
        # الإطار الأيمن
        right_frame = tk.Frame(parent, bg='#16213e', width=500)
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        right_frame.pack_propagate(False)
        
        # عنوان القسم
        title_label = tk.Label(right_frame, text="تفاصيل الفاتورة",
                              bg='#16213e', fg='white',
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=15)
        
        # معلومات الفاتورة
        info_frame = tk.Frame(right_frame, bg='#16213e')
        info_frame.pack(fill='x', padx=20, pady=(0, 15))
        
        # رقم الفاتورة
        invoice_info_frame = tk.Frame(info_frame, bg='#16213e')
        invoice_info_frame.pack(fill='x', pady=5)
        
        tk.Label(invoice_info_frame, text="رقم الفاتورة:",
                bg='#16213e', fg='white',
                font=('Arial', 12, 'bold')).pack(side='left')
        
        self.invoice_number = self.generate_invoice_number()
        self.invoice_number_label = tk.Label(invoice_info_frame, text=self.invoice_number,
                                           bg='#16213e', fg='#27ae60',
                                           font=('Arial', 12, 'bold'))
        self.invoice_number_label.pack(side='right')
        
        # التاريخ والوقت
        datetime_frame = tk.Frame(info_frame, bg='#16213e')
        datetime_frame.pack(fill='x', pady=5)
        
        tk.Label(datetime_frame, text="التاريخ والوقت:",
                bg='#16213e', fg='white',
                font=('Arial', 12, 'bold')).pack(side='left')
        
        now = datetime.now().strftime("%Y-%m-%d %H:%M")
        tk.Label(datetime_frame, text=now,
                bg='#16213e', fg='#3498db',
                font=('Arial', 12, 'bold')).pack(side='right')
        
        # جدول أصناف الفاتورة
        table_frame = tk.Frame(right_frame, bg='#16213e')
        table_frame.pack(fill='both', expand=True, padx=20, pady=(0, 15))
        
        # إنشاء الجدول
        columns = ('الصنف', 'السعر', 'الكمية', 'المجموع')
        self.invoice_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.invoice_tree.heading(col, text=col)
            if col == 'الصنف':
                self.invoice_tree.column(col, width=150, anchor='center')
            else:
                self.invoice_tree.column(col, width=80, anchor='center')
        
        # شريط التمرير للجدول
        tree_scrollbar = ttk.Scrollbar(table_frame, orient='vertical',
                                      command=self.invoice_tree.yview)
        self.invoice_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        self.invoice_tree.pack(side='left', fill='both', expand=True)
        tree_scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج بحذف الصنف
        self.invoice_tree.bind('<Double-1>', self.remove_item_from_invoice)
        
        # إطار المجموع
        total_frame = tk.Frame(right_frame, bg='#0f3460')
        total_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        tk.Label(total_frame, text="المجموع الكلي:",
                bg='#0f3460', fg='white',
                font=('Arial', 16, 'bold')).pack(side='left', padx=20, pady=15)
        
        self.total_label = tk.Label(total_frame, text="0.00 ريال",
                                   bg='#0f3460', fg='#27ae60',
                                   font=('Arial', 18, 'bold'))
        self.total_label.pack(side='right', padx=20, pady=15)
    
    def create_invoice_buttons(self, parent):
        """إنشاء أزرار الفاتورة"""
        buttons_frame = tk.Frame(parent, bg='#1a1a2e')
        buttons_frame.pack(fill='x', pady=20)
        
        # زر طباعة الفاتورة
        print_btn = tk.Button(buttons_frame, text="🖨️ طباعة الفاتورة",
                             command=self.print_invoice,
                             bg='#27ae60', fg='white',
                             font=('Arial', 14, 'bold'),
                             relief='flat', cursor='hand2',
                             width=20, height=2)
        print_btn.pack(side='left', padx=10)
        
        # زر حفظ الفاتورة
        save_btn = tk.Button(buttons_frame, text="💾 حفظ الفاتورة",
                            command=self.save_invoice,
                            bg='#3498db', fg='white',
                            font=('Arial', 14, 'bold'),
                            relief='flat', cursor='hand2',
                            width=20, height=2)
        save_btn.pack(side='left', padx=10)
        
        # زر فاتورة جديدة
        new_btn = tk.Button(buttons_frame, text="📄 فاتورة جديدة",
                           command=self.new_invoice,
                           bg='#9b59b6', fg='white',
                           font=('Arial', 14, 'bold'),
                           relief='flat', cursor='hand2',
                           width=20, height=2)
        new_btn.pack(side='right', padx=10)
        
        # زر حذف صنف
        remove_btn = tk.Button(buttons_frame, text="🗑️ حذف الصنف المحدد",
                              command=self.remove_item_from_invoice,
                              bg='#e74c3c', fg='white',
                              font=('Arial', 14, 'bold'),
                              relief='flat', cursor='hand2',
                              width=20, height=2)
        remove_btn.pack(side='right', padx=10)
    
    def generate_invoice_number(self):
        """توليد رقم فاتورة فريد"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M")
        random_num = random.randint(100, 999)
        return f"INV-{timestamp}-{random_num}"
    
    def refresh_available_items(self):
        """تحديث قائمة الأصناف المتاحة"""
        self.items_listbox.delete(0, tk.END)
        self.available_items = self.db.get_all_items()
        
        for item in self.available_items:
            display_text = f"{item[1]} - {item[2]:.2f} ريال ({item[3]})"
            self.items_listbox.insert(tk.END, display_text)
    
    def filter_items(self, event=None):
        """تصفية الأصناف حسب البحث"""
        search_term = self.search_var.get().lower()
        self.items_listbox.delete(0, tk.END)
        
        for item in self.available_items:
            if search_term in item[1].lower() or search_term in item[3].lower():
                display_text = f"{item[1]} - {item[2]:.2f} ريال ({item[3]})"
                self.items_listbox.insert(tk.END, display_text)
    
    def add_item_to_invoice(self, event=None):
        """إضافة صنف للفاتورة"""
        selection = self.items_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف من القائمة")
            return
        
        try:
            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
            return
        
        # الحصول على بيانات الصنف
        selected_index = selection[0]
        # البحث عن الصنف المطابق في القائمة المفلترة
        filtered_items = []
        search_term = self.search_var.get().lower()
        
        for item in self.available_items:
            if not search_term or search_term in item[1].lower() or search_term in item[3].lower():
                filtered_items.append(item)
        
        if selected_index >= len(filtered_items):
            return
            
        selected_item = filtered_items[selected_index]
        
        # إنشاء عنصر الفاتورة
        invoice_item = {
            'id': selected_item[0],
            'name': selected_item[1],
            'price': selected_item[2],
            'quantity': quantity,
            'subtotal': selected_item[2] * quantity
        }
        
        # البحث عن الصنف في الفاتورة الحالية
        existing_item = None
        for i, item in enumerate(self.invoice_items):
            if item['id'] == invoice_item['id']:
                existing_item = i
                break
        
        if existing_item is not None:
            # تحديث الكمية إذا كان الصنف موجود
            self.invoice_items[existing_item]['quantity'] += quantity
            self.invoice_items[existing_item]['subtotal'] = (
                self.invoice_items[existing_item]['price'] * 
                self.invoice_items[existing_item]['quantity']
            )
        else:
            # إضافة صنف جديد
            self.invoice_items.append(invoice_item)
        
        # تحديث عرض الفاتورة
        self.update_invoice_display()
        
        # إعادة تعيين الكمية
        self.quantity_var.set("1")
    
    def remove_item_from_invoice(self, event=None):
        """حذف صنف من الفاتورة"""
        selection = self.invoice_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return
        
        # الحصول على فهرس الصنف
        item_index = self.invoice_tree.index(selection[0])
        
        # حذف الصنف من القائمة
        if 0 <= item_index < len(self.invoice_items):
            removed_item = self.invoice_items.pop(item_index)
            messagebox.showinfo("تم", f"تم حذف '{removed_item['name']}' من الفاتورة")
            
            # تحديث عرض الفاتورة
            self.update_invoice_display()
    
    def update_invoice_display(self):
        """تحديث عرض الفاتورة"""
        # مسح الجدول
        for item in self.invoice_tree.get_children():
            self.invoice_tree.delete(item)
        
        # إضافة الأصناف
        self.total_amount = 0.0
        for item in self.invoice_items:
            self.invoice_tree.insert('', 'end', values=(
                item['name'],
                f"{item['price']:.2f}",
                item['quantity'],
                f"{item['subtotal']:.2f}"
            ))
            self.total_amount += item['subtotal']
        
        # تحديث المجموع
        self.total_label.config(text=f"{self.total_amount:.2f} ريال")
    
    def print_invoice(self):
        """طباعة الفاتورة الحرارية"""
        if not self.invoice_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف في الفاتورة")
            return
        
        try:
            self.thermal_printer.print_invoice(
                self.invoice_number,
                self.invoice_items,
                self.total_amount
            )
            messagebox.showinfo("نجح", "تم إرسال الفاتورة للطباعة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الطباعة: {str(e)}")
    
    def save_invoice(self):
        """حفظ الفاتورة في قاعدة البيانات"""
        if not self.invoice_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف في الفاتورة")
            return
        
        try:
            self.db.save_invoice(self.invoice_number, self.invoice_items, self.total_amount)
            messagebox.showinfo("نجح", "تم حفظ الفاتورة بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
    
    def new_invoice(self):
        """إنشاء فاتورة جديدة"""
        if self.invoice_items:
            if not messagebox.askyesno("تأكيد", "هل تريد إنشاء فاتورة جديدة؟ سيتم فقدان البيانات الحالية."):
                return
        
        self.reset_invoice()
    
    def reset_invoice(self):
        """إعادة تعيين الفاتورة"""
        self.invoice_items = []
        self.total_amount = 0.0
        self.invoice_number = self.generate_invoice_number()
        self.invoice_number_label.config(text=self.invoice_number)
        self.update_invoice_display()
        self.quantity_var.set("1")
        self.search_var.set("")
        self.refresh_available_items()
