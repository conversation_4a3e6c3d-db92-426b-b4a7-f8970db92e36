#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف تنفيذي لنظام نقاط البيع
Create Executable for POS System

فكرة وتنفيذ: المهندس سيف رافع
هذا البرنامج مجاني لوجه الله تعالى
"""

import os
import sys
import subprocess
import shutil

def print_banner():
    """طباعة شعار البناء"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🔨 إنشاء ملف تنفيذي لنظام نقاط البيع 🔨                   ║
    ║           Creating POS System Executable                    ║
    ║                                                              ║
    ║    👨‍💻 فكرة وتنفيذ: المهندس سيف رافع                        ║
    ║    💝 هذا البرنامج مجاني لوجه الله تعالى                     ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("📦 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت PyInstaller بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PyInstaller")
        return False

def check_files():
    """فحص الملفات المطلوبة"""
    required_files = [
        'start_pos_final.py',
        'web_interface.html',
        'app.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة متوفرة")
    return True

def create_executable():
    """إنشاء الملف التنفيذي"""
    print("🔨 إنشاء الملف التنفيذي...")
    
    # تنظيف المجلدات السابقة
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # أمر PyInstaller
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--console',
        '--name', 'نظام_نقاط_البيع',
        '--distpath', 'dist',
        '--workpath', 'build'
    ]
    
    # إضافة الملفات المطلوبة
    files_to_include = [
        'web_interface.html',
        'app.js',
        'invoice_template.html',
        'advanced_features.js',
        'invoice_design.html',
        'printer_settings.html'
    ]
    
    for file in files_to_include:
        if os.path.exists(file):
            cmd.extend(['--add-data', f'{file};.'])
    
    cmd.append('start_pos_final.py')
    
    try:
        print("🔄 تشغيل PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم إنشاء الملف التنفيذي بنجاح!")
            
            # التحقق من وجود الملف
            exe_path = os.path.join('dist', 'نظام_نقاط_البيع.exe')
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"📁 مسار الملف: {exe_path}")
                print(f"📏 حجم الملف: {size:.1f} MB")
                return True
            else:
                print("❌ لم يتم العثور على الملف التنفيذي")
                return False
        else:
            print("❌ فشل في إنشاء الملف التنفيذي")
            if result.stderr:
                print("تفاصيل الخطأ:")
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def create_readme():
    """إنشاء ملف README"""
    readme_content = """# نظام نقاط البيع المتقدم

## الإصدار الأول - النسخة الأولى من برنامج الفواتير

### فكرة وتنفيذ: المهندس سيف رافع
### هذا البرنامج مجاني لوجه الله تعالى

---

## كيفية التشغيل:

1. شغل الملف التنفيذي `نظام_نقاط_البيع.exe`
2. سيتم فتح المتصفح تلقائياً
3. إذا لم يفتح المتصفح، افتح: http://localhost:8080

## الميزات الرئيسية:

✅ إصدار وطباعة الفواتير مع معاينة جميلة
✅ رفع وإدارة شعار المحل مع خيارات المحاذاة
✅ تخصيص كامل لتصميم الفاتورة (ألوان، خطوط، أبعاد)
✅ إعدادات طباعة متقدمة مع اختبار الطباعة
✅ إدارة الأصناف والمخزون
✅ تقارير وإحصائيات
✅ واجهة متجاوبة تعمل على جميع الأجهزة

## نصائح الاستخدام:

• ارفع شعار محلك من تبويب "الإعدادات"
• خصص تصميم الفاتورة من تبويب "تصميم الفاتورة"
• اختبر الطباعة من تبويب "إعدادات الطباعة"
• استخدم "معاينة الفاتورة" دائماً قبل الطباعة
• احفظ إعداداتك بانتظام

## متطلبات النظام:

• Windows 7 أو أحدث
• 100 MB مساحة فارغة
• متصفح ويب (Chrome, Firefox, Edge)

---

**"وَمَن يَعْمَلْ مِثْقَالَ ذَرَّةٍ خَيْرًا يَرَهُ"**

نسأل الله أن ينفع به ويجعله في ميزان حسناتنا

© 2025 - المهندس سيف رافع
"""
    
    try:
        with open('dist/README.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ تم إنشاء ملف README")
    except Exception as e:
        print(f"⚠️ فشل في إنشاء README: {e}")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print("🔍 فحص المتطلبات...")
    
    # فحص الملفات
    if not check_files():
        print("\n❌ فشل في فحص الملفات المطلوبة")
        input("اضغط Enter للخروج...")
        return
    
    # فحص PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller متوفر")
    except ImportError:
        print("❌ PyInstaller غير مثبت")
        if not install_pyinstaller():
            print("\n❌ فشل في تثبيت PyInstaller")
            input("اضغط Enter للخروج...")
            return
    
    # إنشاء الملف التنفيذي
    print("\n🔨 بدء إنشاء الملف التنفيذي...")
    if create_executable():
        print("\n📝 إنشاء ملفات إضافية...")
        create_readme()
        
        print("\n" + "="*70)
        print("🎉 تم إنشاء نظام نقاط البيع بنجاح!")
        print("="*70)
        print("📁 الملفات في مجلد: dist/")
        print("🚀 شغل الملف: نظام_نقاط_البيع.exe")
        print("📖 اقرأ ملف: README.txt للتعليمات")
        print("="*70)
        print("💝 نسأل الله أن ينفع به ويجعله في ميزان حسناتنا")
        print("👨‍💻 فكرة وتنفيذ: المهندس سيف رافع")
        print("="*70)
        
        # فتح مجلد النتائج
        try:
            os.startfile('dist')
            print("📂 تم فتح مجلد النتائج")
        except:
            pass
            
    else:
        print("\n❌ فشل في إنشاء الملف التنفيذي")
        print("💡 تأكد من:")
        print("   • وجود جميع الملفات المطلوبة")
        print("   • تثبيت Python بشكل صحيح")
        print("   • عدم وجود برامج مكافحة فيروسات تمنع العملية")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
