<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة - مقهى البلايستيشن</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
        }
        
        .invoice-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: gradient 3s ease infinite;
        }
        
        @keyframes gradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .invoice-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .shop-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #00d4aa, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .shop-name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }
        
        .shop-address {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            padding: 40px;
            background: #f8f9fa;
        }
        
        .info-section {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-right: 4px solid #667eea;
        }
        
        .info-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1a1a2e;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .info-value {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 8px;
        }
        
        .invoice-items {
            padding: 0 40px;
        }
        
        .items-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a2e;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }
        
        .items-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .items-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 15px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .items-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 1rem;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table tr:hover {
            background: #e3f2fd;
            transform: scale(1.01);
            transition: all 0.3s ease;
        }
        
        .item-name {
            font-weight: 600;
            color: #1a1a2e;
        }
        
        .item-price, .item-total {
            color: #00d4aa;
            font-weight: 600;
        }
        
        .invoice-total {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .invoice-total::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: slide 2s infinite;
        }
        
        @keyframes slide {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .total-label {
            font-size: 1.5rem;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .total-amount {
            font-size: 3rem;
            font-weight: 700;
            color: #00d4aa;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }
        
        .invoice-footer {
            background: #f8f9fa;
            padding: 30px 40px;
            text-align: center;
        }
        
        .footer-message {
            font-size: 1.2rem;
            color: #1a1a2e;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .thank-you {
            font-size: 1rem;
            color: #666;
            font-style: italic;
        }
        
        .print-button {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(135deg, #00d4aa, #4ecdc4);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(0, 212, 170, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .print-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 212, 170, 0.4);
        }
        
        .print-button:active {
            transform: translateY(-1px);
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .invoice-container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .print-button {
                display: none;
            }
        }
        
        @media (max-width: 768px) {
            .invoice-info {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .invoice-header,
            .invoice-items,
            .invoice-total,
            .invoice-footer {
                padding: 20px;
            }
            
            .shop-name {
                font-size: 2rem;
            }
            
            .total-amount {
                font-size: 2.5rem;
            }
            
            .items-table th,
            .items-table td {
                padding: 10px 8px;
                font-size: 0.9rem;
            }
        }
        
        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .fade-in {
            animation: fadeIn 1s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="invoice-container fade-in">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="shop-logo floating-animation">🎮</div>
            <h1 class="shop-name">{{SHOP_NAME}}</h1>
            <p class="shop-address">{{SHOP_ADDRESS}}</p>
        </div>
        
        <!-- معلومات الفاتورة -->
        <div class="invoice-info">
            <div class="info-section">
                <h3 class="info-title">📄 معلومات الفاتورة</h3>
                <div class="info-value"><strong>رقم الفاتورة:</strong> {{INVOICE_NUMBER}}</div>
                <div class="info-value"><strong>التاريخ:</strong> {{INVOICE_DATE}}</div>
                <div class="info-value"><strong>الوقت:</strong> {{INVOICE_TIME}}</div>
            </div>
            
            <div class="info-section">
                <h3 class="info-title">🏪 معلومات المحل</h3>
                <div class="info-value"><strong>الهاتف:</strong> {{SHOP_PHONE}}</div>
                <div class="info-value"><strong>البريد:</strong> {{SHOP_EMAIL}}</div>
                <div class="info-value"><strong>نوع الفاتورة:</strong> {{RECEIPT_TYPE}}</div>
            </div>
        </div>
        
        <!-- أصناف الفاتورة -->
        <div class="invoice-items">
            <h2 class="items-title">🛍️ تفاصيل الطلب</h2>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th>الصنف</th>
                        <th>السعر الواحد</th>
                        <th>الكمية</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    {{INVOICE_ITEMS}}
                </tbody>
            </table>
        </div>
        
        <!-- المجموع الكلي -->
        <div class="invoice-total">
            <div class="total-label">المجموع الكلي</div>
            <div class="total-amount">{{TOTAL_AMOUNT}} ريال</div>
        </div>
        
        <!-- ختام الفاتورة -->
        <div class="invoice-footer">
            <div class="footer-message">{{FOOTER_MESSAGE}}</div>
            <div class="thank-you">شكراً لزيارتكم ونتطلع لخدمتكم مرة أخرى</div>
        </div>
    </div>
    
    <!-- زر الطباعة -->
    <button class="print-button" onclick="window.print()">
        🖨️ طباعة الفاتورة
    </button>
    
    <script>
        // تأثيرات JavaScript إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير تدرجي للظهور
            const elements = document.querySelectorAll('.info-section, .items-table tr');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
                el.classList.add('fade-in');
            });
            
            // تأثير hover للصفوف
            const rows = document.querySelectorAll('.items-table tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
