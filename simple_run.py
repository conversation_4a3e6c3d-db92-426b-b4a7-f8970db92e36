#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط لنظام نقاط البيع
Simple launcher for POS System
"""

import sys
import os

def install_missing_packages():
    """تثبيت المكتبات المفقودة"""
    import subprocess
    
    packages = ['Pillow', 'reportlab', 'arabic-reshaper', 'python-bidi']
    
    print("🔄 تثبيت المكتبات المطلوبة...")
    
    for package in packages:
        try:
            print(f"📦 تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package, '--quiet'])
            print(f"✅ {package}")
        except:
            print(f"❌ فشل في تثبيت {package}")

def main():
    """الدالة الرئيسية"""
    print("🎮 نظام نقاط البيع - مقهى البلايستيشن")
    print("=" * 50)
    
    # محاولة استيراد المكتبات
    missing = []
    
    try:
        import tkinter as tk
    except ImportError:
        print("❌ tkinter مفقود (يجب أن يكون مدمج مع Python)")
        return
    
    try:
        from PIL import Image
    except ImportError:
        missing.append('Pillow')
    
    try:
        from reportlab.pdfgen import canvas
    except ImportError:
        missing.append('reportlab')
    
    try:
        import arabic_reshaper
    except ImportError:
        missing.append('arabic-reshaper')
    
    try:
        from bidi.algorithm import get_display
    except ImportError:
        missing.append('python-bidi')
    
    # تثبيت المكتبات المفقودة
    if missing:
        print(f"⚠️ مكتبات مفقودة: {', '.join(missing)}")
        response = input("هل تريد تثبيتها؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            for package in missing:
                try:
                    import subprocess
                    print(f"📦 تثبيت {package}...")
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                    print(f"✅ تم تثبيت {package}")
                except Exception as e:
                    print(f"❌ فشل في تثبيت {package}: {e}")
        else:
            print("❌ لا يمكن تشغيل البرنامج بدون المكتبات المطلوبة")
            return
    
    # إنشاء المجلدات المطلوبة
    directories = ['logos', 'receipts', 'exports', 'backups']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    # تشغيل البرنامج
    try:
        print("🚀 تشغيل البرنامج...")
        from main import CafePOSApp
        app = CafePOSApp()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
