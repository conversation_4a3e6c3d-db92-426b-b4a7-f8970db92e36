import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sqlite3

class ReportsManager:
    def __init__(self, database):
        self.db = database
        
    def open_reports_window(self, parent):
        """فتح نافذة التقارير والإحصائيات"""
        # مسح المحتوى السابق
        for widget in parent.winfo_children():
            widget.destroy()
            
        # إنشاء الإطار الرئيسي
        main_frame = tk.Frame(parent, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(main_frame, text="📊 التقارير والإحصائيات",
                              bg='#1a1a2e', fg='white',
                              font=('Arial', 20, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار العلوية
        buttons_frame = tk.Frame(main_frame, bg='#1a1a2e')
        buttons_frame.pack(fill='x', pady=(0, 20))
        
        # أزرار التقارير
        daily_btn = tk.Button(buttons_frame, text="📅 تقرير يومي",
                             command=self.show_daily_report,
                             bg='#27ae60', fg='white',
                             font=('Arial', 12, 'bold'),
                             relief='flat', cursor='hand2',
                             width=15, height=2)
        daily_btn.pack(side='left', padx=(0, 10))
        
        weekly_btn = tk.Button(buttons_frame, text="📆 تقرير أسبوعي",
                              command=self.show_weekly_report,
                              bg='#3498db', fg='white',
                              font=('Arial', 12, 'bold'),
                              relief='flat', cursor='hand2',
                              width=15, height=2)
        weekly_btn.pack(side='left', padx=10)
        
        monthly_btn = tk.Button(buttons_frame, text="🗓️ تقرير شهري",
                               command=self.show_monthly_report,
                               bg='#9b59b6', fg='white',
                               font=('Arial', 12, 'bold'),
                               relief='flat', cursor='hand2',
                               width=15, height=2)
        monthly_btn.pack(side='left', padx=10)
        
        items_btn = tk.Button(buttons_frame, text="📦 تقرير الأصناف",
                             command=self.show_items_report,
                             bg='#f39c12', fg='white',
                             font=('Arial', 12, 'bold'),
                             relief='flat', cursor='hand2',
                             width=15, height=2)
        items_btn.pack(side='left', padx=10)
        
        export_btn = tk.Button(buttons_frame, text="📤 تصدير التقرير",
                              command=self.export_report,
                              bg='#e74c3c', fg='white',
                              font=('Arial', 12, 'bold'),
                              relief='flat', cursor='hand2',
                              width=15, height=2)
        export_btn.pack(side='right')
        
        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg='#16213e')
        content_frame.pack(fill='both', expand=True)
        
        # الجانب الأيسر - الإحصائيات
        self.create_stats_panel(content_frame)
        
        # الجانب الأيمن - قائمة الفواتير
        self.create_invoices_panel(content_frame)
        
        # عرض التقرير اليومي افتراضياً
        self.show_daily_report()
    
    def create_stats_panel(self, parent):
        """إنشاء لوحة الإحصائيات"""
        # الإطار الأيسر
        left_frame = tk.Frame(parent, bg='#16213e', width=400)
        left_frame.pack(side='left', fill='both', expand=True, padx=(20, 10), pady=20)
        left_frame.pack_propagate(False)
        
        # عنوان القسم
        stats_title = tk.Label(left_frame, text="الإحصائيات",
                              bg='#16213e', fg='white',
                              font=('Arial', 16, 'bold'))
        stats_title.pack(pady=(0, 20))
        
        # إطار بطاقات الإحصائيات
        self.stats_cards_frame = tk.Frame(left_frame, bg='#16213e')
        self.stats_cards_frame.pack(fill='x', pady=(0, 20))
        
        # إطار الرسم البياني (مبسط)
        chart_frame = tk.Frame(left_frame, bg='#0f3460', height=200)
        chart_frame.pack(fill='x', pady=(0, 20))
        chart_frame.pack_propagate(False)
        
        chart_title = tk.Label(chart_frame, text="📈 الرسم البياني للمبيعات",
                              bg='#0f3460', fg='white',
                              font=('Arial', 14, 'bold'))
        chart_title.pack(pady=20)
        
        # رسالة مؤقتة للرسم البياني
        chart_msg = tk.Label(chart_frame, text="الرسم البياني قيد التطوير",
                            bg='#0f3460', fg='#95a5a6',
                            font=('Arial', 12))
        chart_msg.pack(expand=True)
        
        # إطار الأصناف الأكثر مبيعاً
        top_items_frame = tk.Frame(left_frame, bg='#0f3460')
        top_items_frame.pack(fill='both', expand=True)
        
        top_items_title = tk.Label(top_items_frame, text="🏆 الأصناف الأكثر مبيعاً",
                                  bg='#0f3460', fg='white',
                                  font=('Arial', 14, 'bold'))
        top_items_title.pack(pady=(15, 10))
        
        # قائمة الأصناف الأكثر مبيعاً
        self.top_items_listbox = tk.Listbox(top_items_frame,
                                           font=('Arial', 10),
                                           bg='white', fg='black',
                                           height=8)
        self.top_items_listbox.pack(fill='both', expand=True, padx=15, pady=(0, 15))
    
    def create_invoices_panel(self, parent):
        """إنشاء لوحة قائمة الفواتير"""
        # الإطار الأيمن
        right_frame = tk.Frame(parent, bg='#16213e', width=500)
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 20), pady=20)
        right_frame.pack_propagate(False)
        
        # عنوان القسم
        invoices_title = tk.Label(right_frame, text="قائمة الفواتير",
                                 bg='#16213e', fg='white',
                                 font=('Arial', 16, 'bold'))
        invoices_title.pack(pady=(0, 20))
        
        # إطار البحث والتصفية
        filter_frame = tk.Frame(right_frame, bg='#16213e')
        filter_frame.pack(fill='x', pady=(0, 15))
        
        # حقل البحث
        tk.Label(filter_frame, text="🔍 البحث:",
                bg='#16213e', fg='white',
                font=('Arial', 12)).pack(side='left')
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(filter_frame, textvariable=self.search_var,
                               font=('Arial', 12), width=20)
        search_entry.pack(side='left', padx=(10, 20))
        search_entry.bind('<KeyRelease>', self.filter_invoices)
        
        # تصفية حسب التاريخ
        tk.Label(filter_frame, text="التاريخ:",
                bg='#16213e', fg='white',
                font=('Arial', 12)).pack(side='left')
        
        self.date_var = tk.StringVar()
        date_entry = tk.Entry(filter_frame, textvariable=self.date_var,
                             font=('Arial', 12), width=12)
        date_entry.pack(side='left', padx=(10, 0))
        date_entry.bind('<KeyRelease>', self.filter_invoices)
        
        # جدول الفواتير
        table_frame = tk.Frame(right_frame, bg='#16213e')
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء الجدول
        columns = ('رقم الفاتورة', 'المبلغ', 'التاريخ', 'الوقت')
        self.invoices_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تخصيص الأعمدة
        self.invoices_tree.heading('رقم الفاتورة', text='رقم الفاتورة')
        self.invoices_tree.heading('المبلغ', text='المبلغ (ريال)')
        self.invoices_tree.heading('التاريخ', text='التاريخ')
        self.invoices_tree.heading('الوقت', text='الوقت')
        
        self.invoices_tree.column('رقم الفاتورة', width=120, anchor='center')
        self.invoices_tree.column('المبلغ', width=100, anchor='center')
        self.invoices_tree.column('التاريخ', width=100, anchor='center')
        self.invoices_tree.column('الوقت', width=80, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)
        
        self.invoices_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج بعرض تفاصيل الفاتورة
        self.invoices_tree.bind('<Double-1>', self.show_invoice_details)
    
    def show_daily_report(self):
        """عرض التقرير اليومي"""
        today = datetime.now().strftime('%Y-%m-%d')
        self.current_report_type = "يومي"
        self.current_date_filter = today
        
        # تحديث الإحصائيات
        self.update_stats(today, today)
        
        # تحديث قائمة الفواتير
        self.update_invoices_list(today, today)
        
        # تحديث الأصناف الأكثر مبيعاً
        self.update_top_items(today, today)
    
    def show_weekly_report(self):
        """عرض التقرير الأسبوعي"""
        today = datetime.now()
        week_start = (today - timedelta(days=today.weekday())).strftime('%Y-%m-%d')
        week_end = today.strftime('%Y-%m-%d')
        
        self.current_report_type = "أسبوعي"
        self.current_date_filter = f"{week_start} إلى {week_end}"
        
        self.update_stats(week_start, week_end)
        self.update_invoices_list(week_start, week_end)
        self.update_top_items(week_start, week_end)
    
    def show_monthly_report(self):
        """عرض التقرير الشهري"""
        today = datetime.now()
        month_start = today.replace(day=1).strftime('%Y-%m-%d')
        month_end = today.strftime('%Y-%m-%d')
        
        self.current_report_type = "شهري"
        self.current_date_filter = f"{month_start} إلى {month_end}"
        
        self.update_stats(month_start, month_end)
        self.update_invoices_list(month_start, month_end)
        self.update_top_items(month_start, month_end)
    
    def show_items_report(self):
        """عرض تقرير الأصناف"""
        self.current_report_type = "الأصناف"
        
        # إنشاء نافذة منفصلة لتقرير الأصناف
        items_window = tk.Toplevel()
        items_window.title("تقرير الأصناف")
        items_window.geometry("800x600")
        items_window.configure(bg='#1a1a2e')
        
        # العنوان
        title_label = tk.Label(items_window, text="📦 تقرير الأصناف التفصيلي",
                              bg='#1a1a2e', fg='white',
                              font=('Arial', 18, 'bold'))
        title_label.pack(pady=20)
        
        # إطار الجدول
        table_frame = tk.Frame(items_window, bg='#16213e')
        table_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # جدول الأصناف
        columns = ('اسم الصنف', 'السعر', 'الفئة', 'مرات البيع', 'إجمالي المبيعات')
        items_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            items_tree.heading(col, text=col)
            items_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        items_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=items_tree.yview)
        items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        items_tree.pack(side='left', fill='both', expand=True)
        items_scrollbar.pack(side='right', fill='y')
        
        # تحميل بيانات الأصناف
        self.load_items_report_data(items_tree)
    
    def update_stats(self, start_date, end_date):
        """تحديث الإحصائيات"""
        # مسح البطاقات الحالية
        for widget in self.stats_cards_frame.winfo_children():
            widget.destroy()
        
        # الحصول على الإحصائيات من قاعدة البيانات
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # إجمالي الفواتير والمبيعات
        cursor.execute('''SELECT COUNT(*) as invoice_count, SUM(total_amount) as total_sales 
                         FROM invoices WHERE DATE(created_at) BETWEEN ? AND ?''', 
                      (start_date, end_date))
        stats = cursor.fetchone()
        
        invoice_count = stats[0] if stats[0] else 0
        total_sales = stats[1] if stats[1] else 0.0
        avg_invoice = total_sales / invoice_count if invoice_count > 0 else 0.0
        
        conn.close()
        
        # إنشاء بطاقات الإحصائيات
        stats_data = [
            ("عدد الفواتير", str(invoice_count), '#3498db'),
            ("إجمالي المبيعات", f"{total_sales:.2f} ريال", '#27ae60'),
            ("متوسط الفاتورة", f"{avg_invoice:.2f} ريال", '#9b59b6')
        ]
        
        for i, (title, value, color) in enumerate(stats_data):
            card_frame = tk.Frame(self.stats_cards_frame, bg=color, height=80)
            card_frame.pack(fill='x', pady=5)
            card_frame.pack_propagate(False)
            
            title_label = tk.Label(card_frame, text=title,
                                  bg=color, fg='white',
                                  font=('Arial', 12, 'bold'))
            title_label.pack(pady=(15, 5))
            
            value_label = tk.Label(card_frame, text=value,
                                  bg=color, fg='white',
                                  font=('Arial', 14, 'bold'))
            value_label.pack()
    
    def update_invoices_list(self, start_date, end_date):
        """تحديث قائمة الفواتير"""
        # مسح البيانات الحالية
        for item in self.invoices_tree.get_children():
            self.invoices_tree.delete(item)
        
        # الحصول على الفواتير من قاعدة البيانات
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''SELECT invoice_number, total_amount, created_at 
                         FROM invoices WHERE DATE(created_at) BETWEEN ? AND ? 
                         ORDER BY created_at DESC''', (start_date, end_date))
        
        invoices = cursor.fetchall()
        conn.close()
        
        # إضافة البيانات للجدول
        for invoice in invoices:
            # تقسيم التاريخ والوقت
            datetime_str = invoice[2]
            date_part = datetime_str[:10]
            time_part = datetime_str[11:16]
            
            self.invoices_tree.insert('', 'end', values=(
                invoice[0],  # رقم الفاتورة
                f"{invoice[1]:.2f}",  # المبلغ
                date_part,  # التاريخ
                time_part   # الوقت
            ))
    
    def update_top_items(self, start_date, end_date):
        """تحديث الأصناف الأكثر مبيعاً"""
        # مسح القائمة الحالية
        self.top_items_listbox.delete(0, tk.END)
        
        # الحصول على الأصناف الأكثر مبيعاً
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''SELECT ii.item_name, SUM(ii.quantity) as total_qty, 
                                SUM(ii.subtotal) as total_sales
                         FROM invoice_items ii
                         JOIN invoices i ON ii.invoice_id = i.id
                         WHERE DATE(i.created_at) BETWEEN ? AND ?
                         GROUP BY ii.item_name
                         ORDER BY total_qty DESC
                         LIMIT 10''', (start_date, end_date))
        
        top_items = cursor.fetchall()
        conn.close()
        
        # إضافة البيانات للقائمة
        for i, item in enumerate(top_items, 1):
            display_text = f"{i}. {item[0]} - {item[1]} قطعة ({item[2]:.2f} ريال)"
            self.top_items_listbox.insert(tk.END, display_text)
    
    def filter_invoices(self, event=None):
        """تصفية الفواتير حسب البحث"""
        search_term = self.search_var.get().lower()
        date_filter = self.date_var.get()
        
        # إعادة تحميل البيانات مع التصفية
        # هذه وظيفة يمكن تطويرها أكثر
        pass
    
    def show_invoice_details(self, event=None):
        """عرض تفاصيل الفاتورة"""
        selection = self.invoices_tree.selection()
        if not selection:
            return
        
        invoice_data = self.invoices_tree.item(selection[0])['values']
        invoice_number = invoice_data[0]
        
        # إنشاء نافذة تفاصيل الفاتورة
        details_window = tk.Toplevel()
        details_window.title(f"تفاصيل الفاتورة - {invoice_number}")
        details_window.geometry("600x500")
        details_window.configure(bg='#1a1a2e')
        
        # العنوان
        title_label = tk.Label(details_window, text=f"📄 تفاصيل الفاتورة: {invoice_number}",
                              bg='#1a1a2e', fg='white',
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # الحصول على تفاصيل الفاتورة
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''SELECT ii.item_name, ii.item_price, ii.quantity, ii.subtotal
                         FROM invoice_items ii
                         JOIN invoices i ON ii.invoice_id = i.id
                         WHERE i.invoice_number = ?''', (invoice_number,))
        
        items = cursor.fetchall()
        conn.close()
        
        # جدول التفاصيل
        details_frame = tk.Frame(details_window, bg='#16213e')
        details_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        columns = ('الصنف', 'السعر', 'الكمية', 'المجموع')
        details_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            details_tree.heading(col, text=col)
            details_tree.column(col, width=140, anchor='center')
        
        # إضافة البيانات
        total = 0
        for item in items:
            details_tree.insert('', 'end', values=(
                item[0],  # اسم الصنف
                f"{item[1]:.2f}",  # السعر
                item[2],  # الكمية
                f"{item[3]:.2f}"   # المجموع
            ))
            total += item[3]
        
        details_tree.pack(fill='both', expand=True)
        
        # المجموع الكلي
        total_label = tk.Label(details_window, text=f"المجموع الكلي: {total:.2f} ريال",
                              bg='#1a1a2e', fg='#27ae60',
                              font=('Arial', 14, 'bold'))
        total_label.pack(pady=20)
    
    def load_items_report_data(self, tree):
        """تحميل بيانات تقرير الأصناف"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # الحصول على إحصائيات الأصناف
        cursor.execute('''SELECT i.name, i.price, i.category,
                                COALESCE(SUM(ii.quantity), 0) as total_sold,
                                COALESCE(SUM(ii.subtotal), 0) as total_revenue
                         FROM items i
                         LEFT JOIN invoice_items ii ON i.name = ii.item_name
                         GROUP BY i.id, i.name, i.price, i.category
                         ORDER BY total_sold DESC''')
        
        items_data = cursor.fetchall()
        conn.close()
        
        # إضافة البيانات للجدول
        for item in items_data:
            tree.insert('', 'end', values=(
                item[0],  # اسم الصنف
                f"{item[1]:.2f}",  # السعر
                item[2],  # الفئة
                item[3],  # مرات البيع
                f"{item[4]:.2f}"   # إجمالي المبيعات
            ))
    
    def export_report(self):
        """تصدير التقرير"""
        try:
            from tkinter import filedialog
            import csv
            
            filename = filedialog.asksaveasfilename(
                title="تصدير التقرير",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("Text files", "*.txt"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # كتابة العناوين
                    writer.writerow(['رقم الفاتورة', 'المبلغ', 'التاريخ', 'الوقت'])
                    
                    # كتابة البيانات
                    for child in self.invoices_tree.get_children():
                        values = self.invoices_tree.item(child)['values']
                        writer.writerow(values)
                
                messagebox.showinfo("نجح", "تم تصدير التقرير بنجاح")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير التقرير: {str(e)}")
