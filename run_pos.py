#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام نقاط البيع المتقدم
Advanced POS System Launcher
"""

import os
import json
import webbrowser
import threading
import time
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler

class POSHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path == '/':
            self.path = '/web_interface.html'
        elif self.path.startswith('/api/'):
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            if self.path == '/api/data':
                data = {
                    'items': [
                        {'id': 1, 'name': 'ساعة بلايستيشن', 'price': 15.0, 'category': 'ألعاب'},
                        {'id': 2, 'name': 'قهوة عربية', 'price': 8.0, 'category': 'مشروبات'},
                        {'id': 3, 'name': 'شاي أحمر', 'price': 5.0, 'category': 'مشروبات'},
                        {'id': 4, 'name': 'عصير برتقال', 'price': 10.0, 'category': 'مشروبات'},
                        {'id': 5, 'name': 'ساندويش تونة', 'price': 20.0, 'category': 'طعام'},
                        {'id': 6, 'name': 'بيتزا صغيرة', 'price': 35.0, 'category': 'طعام'},
                        {'id': 7, 'name': 'مياه معدنية', 'price': 2.0, 'category': 'مشروبات'},
                        {'id': 8, 'name': 'كولا', 'price': 6.0, 'category': 'مشروبات'}
                    ],
                    'stats': {
                        'today_invoices': 12,
                        'today_sales': 285.50,
                        'total_items': 8,
                        'avg_invoice': 23.79
                    },
                    'settings': {
                        'shop_name': 'مقهى البلايستيشن',
                        'shop_address': 'الرياض - المملكة العربية السعودية',
                        'shop_phone': '+966 11 234 5678',
                        'shop_email': '<EMAIL>',
                        'footer_message': 'نسعد بخدمتكم'
                    }
                }
            elif self.path == '/api/printers':
                data = {
                    'printers': [
                        {'name': 'طابعة حرارية - USB', 'is_default': True, 'status': 'online'},
                        {'name': 'Microsoft Print to PDF', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة الشبكة', 'is_default': False, 'status': 'offline'}
                    ]
                }
            else:
                data = {'success': True, 'message': 'تم تنفيذ العملية بنجاح'}
            
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            return
        
        return super().do_GET()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
                action = data.get('action', 'unknown')
                print(f"📝 تم استلام إجراء: {action}")
            except:
                pass
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = {'success': True, 'message': 'تم تنفيذ العملية بنجاح'}
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🎮 نظام نقاط البيع المتقدم - مقهى البلايستيشن 🎮          ║
    ║           Advanced POS System - PlayStation Cafe            ║
    ║                                                              ║
    ║    ✨ الإصدار الأول - النسخة الأولى من برنامج الفواتير ✨     ║
    ║                                                              ║
    ║    👨‍💻 فكرة وتنفيذ: المهندس سيف رافع                        ║
    ║    💝 هذا البرنامج مجاني لوجه الله                          ║
    ║                                                              ║
    ║    📅 التاريخ: {date}                                        ║
    ║    ⏰ الوقت: {time}                                          ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """.format(
        date=datetime.now().strftime("%Y-%m-%d"),
        time=datetime.now().strftime("%H:%M:%S")
    )
    print(banner)

def check_files():
    """فحص الملفات المطلوبة"""
    required_files = [
        'web_interface.html',
        'app.js',
        'invoice_template.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة متوفرة")
    return True

def start_server(port=8080):
    """بدء الخادم"""
    try:
        server = HTTPServer(('localhost', port), POSHandler)
        print(f"🌐 الخادم يعمل على: http://localhost:{port}")
        
        # فتح المتصفح بعد ثانيتين
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 تم فتح المتصفح")
            except:
                print("⚠️ لم يتم فتح المتصفح تلقائياً")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n" + "="*70)
        print("🎮 نظام نقاط البيع جاهز للاستخدام!")
        print("="*70)
        print("📋 الميزات المتاحة:")
        print("   • 🧾 إصدار وطباعة الفواتير مع معاينة")
        print("   • 📦 إدارة الأصناف والمخزون")
        print("   • 🎨 تخصيص تصميم الفاتورة بالكامل")
        print("   • 🖨️ إعدادات طباعة متقدمة")
        print("   • 📊 تقارير وإحصائيات مفصلة")
        print("   • ⚙️ إعدادات شاملة مع رفع الشعار")
        print("   • 📱 واجهة متجاوبة تعمل على جميع الأجهزة")
        print("="*70)
        print("💡 نصائح الاستخدام:")
        print("   • استخدم تبويب 'تصميم الفاتورة' لتخصيص الشكل")
        print("   • ارفع شعار محلك من تبويب 'الإعدادات'")
        print("   • اختبر الطباعة من تبويب 'إعدادات الطباعة'")
        print("   • استخدم 'معاينة الفاتورة' قبل الطباعة")
        print("="*70)
        print("⚠️ لإيقاف الخادم: اضغط Ctrl+C")
        print("="*70)
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 إيقاف الخادم...")
        server.shutdown()
        server.server_close()
        print("✅ تم إيقاف الخادم بنجاح")
        print("\n🙏 شكراً لاستخدام نظام نقاط البيع")
        print("💝 نسأل الله أن ينفع به ويجعله في ميزان حسناتنا")
        
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print("🔍 فحص النظام...")
    if not check_files():
        print("\n❌ فشل في فحص الملفات المطلوبة")
        input("اضغط Enter للخروج...")
        return
    
    print("🚀 بدء نظام نقاط البيع...")
    
    try:
        start_server()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
