#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم اختبار بسيط لنظام نقاط البيع
Simple Test Server for POS System
"""

import os
import json
import sqlite3
import webbrowser
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse

class TestPOSHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        """معالجة طلبات GET"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            # إعادة توجيه للصفحة الرئيسية
            self.path = '/web_interface.html'
            return super().do_GET()
        
        elif parsed_path.path == '/api/data':
            # إرجاع بيانات تجريبية
            self.send_json_response(self.get_sample_data())
            return
        
        elif parsed_path.path == '/api/items':
            # إرجاع الأصناف التجريبية
            self.send_json_response({'items': self.get_sample_items()})
            return
        
        elif parsed_path.path == '/api/printers':
            # إرجاع قائمة الطابعات التجريبية
            self.send_json_response({'printers': self.get_sample_printers()})
            return
        
        else:
            # ملفات أخرى
            return super().do_GET()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/action':
            # قراءة البيانات
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                action = data.get('action')
                payload = data.get('data')
                
                result = self.handle_test_action(action, payload)
                self.send_json_response(result)
                
            except Exception as e:
                self.send_json_response({'error': str(e)}, 500)
        
        else:
            self.send_json_response({'error': 'Not found'}, 404)
    
    def send_json_response(self, data, status=200):
        """إرسال استجابة JSON"""
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def get_sample_data(self):
        """الحصول على بيانات تجريبية"""
        return {
            'items': self.get_sample_items(),
            'invoices': [],
            'settings': self.get_sample_settings(),
            'stats': {
                'today_invoices': 5,
                'today_sales': 125.50,
                'total_items': 8,
                'avg_invoice': 25.10
            }
        }
    
    def get_sample_items(self):
        """الحصول على أصناف تجريبية"""
        return [
            {'id': 1, 'name': 'ساعة بلايستيشن', 'price': 15.0, 'category': 'ألعاب'},
            {'id': 2, 'name': 'قهوة عربية', 'price': 8.0, 'category': 'مشروبات'},
            {'id': 3, 'name': 'شاي أحمر', 'price': 5.0, 'category': 'مشروبات'},
            {'id': 4, 'name': 'عصير برتقال', 'price': 10.0, 'category': 'مشروبات'},
            {'id': 5, 'name': 'ساندويش تونة', 'price': 20.0, 'category': 'طعام'},
            {'id': 6, 'name': 'بيتزا صغيرة', 'price': 35.0, 'category': 'طعام'},
            {'id': 7, 'name': 'مياه معدنية', 'price': 2.0, 'category': 'مشروبات'},
            {'id': 8, 'name': 'كولا', 'price': 6.0, 'category': 'مشروبات'}
        ]
    
    def get_sample_settings(self):
        """الحصول على إعدادات تجريبية"""
        return {
            'shop_name': 'مقهى البلايستيشن',
            'shop_address': 'الرياض - المملكة العربية السعودية',
            'shop_phone': '+966 11 234 5678',
            'shop_email': '<EMAIL>',
            'footer_message': 'نسعد بخدمتكم',
            'default_receipt_type': 'thermal_80',
            'auto_print': False
        }
    
    def get_sample_printers(self):
        """الحصول على قائمة طابعات تجريبية"""
        return [
            {'name': 'طابعة حرارية - USB', 'is_default': True, 'status': 'online'},
            {'name': 'Microsoft Print to PDF', 'is_default': False, 'status': 'online'},
            {'name': 'طابعة الشبكة', 'is_default': False, 'status': 'offline'}
        ]
    
    def handle_test_action(self, action, data):
        """معالجة الإجراءات التجريبية"""
        print(f"تم استلام إجراء: {action}")
        
        if action == 'save_invoice':
            print(f"حفظ فاتورة: {data.get('number', 'غير محدد')}")
            return {'success': True, 'message': 'تم حفظ الفاتورة بنجاح'}
        
        elif action == 'add_item':
            print(f"إضافة صنف: {data.get('name', 'غير محدد')}")
            return {'success': True, 'message': 'تم إضافة الصنف بنجاح'}
        
        elif action == 'save_settings':
            print(f"حفظ الإعدادات")
            return {'success': True, 'message': 'تم حفظ الإعدادات بنجاح'}
        
        elif action == 'save_design':
            print(f"حفظ تصميم الفاتورة")
            return {'success': True, 'message': 'تم حفظ التصميم بنجاح'}
        
        elif action == 'save_printer_settings':
            print(f"حفظ إعدادات الطباعة")
            return {'success': True, 'message': 'تم حفظ إعدادات الطباعة بنجاح'}
        
        elif action == 'print_invoice':
            print(f"طباعة فاتورة: {data.get('number', 'غير محدد')}")
            return {'success': True, 'message': 'تم إرسال الفاتورة للطباعة'}
        
        else:
            return {'success': False, 'error': f'إجراء غير معروف: {action}'}

def main():
    """الدالة الرئيسية"""
    port = 8080
    
    print("🎮 خادم اختبار نظام نقاط البيع")
    print("=" * 50)
    print(f"🌐 بدء الخادم على المنفذ {port}")
    
    try:
        server = HTTPServer(('localhost', port), TestPOSHandler)
        print(f"✅ الخادم يعمل على: http://localhost:{port}")
        print("🌐 فتح المتصفح...")
        
        # فتح المتصفح
        webbrowser.open(f'http://localhost:{port}')
        
        print("\n📝 اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 50)
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 إيقاف الخادم...")
        server.shutdown()
        server.server_close()
        print("✅ تم إيقاف الخادم بنجاح")
    
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")

if __name__ == "__main__":
    main()
