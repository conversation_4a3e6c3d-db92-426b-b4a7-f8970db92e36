#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الطباعة المتقدم مع أنواع فواتير متعددة
Advanced printing system with multiple receipt types
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import tempfile
from datetime import datetime
import subprocess
import sys
from PIL import Image, ImageDraw, ImageFont
import win32print
import win32api

class AdvancedPrinter:
    def __init__(self, database):
        self.db = database
        self.receipt_types = {
            'thermal_58': {'width': 58, 'name': 'حراري 58 مم', 'chars_per_line': 32},
            'thermal_70': {'width': 70, 'name': 'حراري 70 مم', 'chars_per_line': 38},
            'thermal_80': {'width': 80, 'name': 'حراري 80 مم', 'chars_per_line': 42},
            'a4': {'width': 210, 'name': 'A4 عادي', 'chars_per_line': 80}
        }
        self.default_printer = self.get_default_printer()
        
    def get_default_printer(self):
        """الحصول على الطابعة الافتراضية"""
        try:
            return win32print.GetDefaultPrinter()
        except:
            return "لا توجد طابعة افتراضية"
    
    def get_available_printers(self):
        """الحصول على قائمة الطابعات المتاحة"""
        try:
            printers = []
            printer_info = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
            for printer in printer_info:
                printers.append(printer[2])
            return printers
        except:
            return ["الطابعة الافتراضية"]
    
    def show_print_dialog(self, invoice_number, items, total_amount, parent_window):
        """عرض نافذة خيارات الطباعة"""
        print_window = tk.Toplevel(parent_window)
        print_window.title("🖨️ خيارات الطباعة")
        print_window.geometry("800x700")
        print_window.configure(bg='#1a1a2e')
        print_window.resizable(False, False)
        
        # جعل النافذة في المقدمة
        print_window.transient(parent_window)
        print_window.grab_set()
        
        # العنوان
        title_frame = tk.Frame(print_window, bg='#0f3460', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame, text="🖨️ إعدادات الطباعة المتقدمة",
                bg='#0f3460', fg='white',
                font=('Segoe UI', 18, 'bold')).pack(expand=True)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(print_window, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # قسم نوع الفاتورة
        receipt_frame = tk.Frame(main_frame, bg='#16213e')
        receipt_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(receipt_frame, text="📄 نوع الفاتورة",
                bg='#16213e', fg='white',
                font=('Segoe UI', 14, 'bold')).pack(pady=10)
        
        self.receipt_type_var = tk.StringVar(value='thermal_80')
        
        for key, info in self.receipt_types.items():
            rb = tk.Radiobutton(receipt_frame, text=f"{info['name']} ({info['width']}mm)",
                               variable=self.receipt_type_var, value=key,
                               bg='#16213e', fg='white', selectcolor='#0f3460',
                               font=('Segoe UI', 12), activebackground='#16213e',
                               activeforeground='white', command=self.update_preview)
            rb.pack(anchor='w', padx=20, pady=5)
        
        # قسم الطابعة
        printer_frame = tk.Frame(main_frame, bg='#16213e')
        printer_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(printer_frame, text="🖨️ اختيار الطابعة",
                bg='#16213e', fg='white',
                font=('Segoe UI', 14, 'bold')).pack(pady=10)
        
        printer_info_frame = tk.Frame(printer_frame, bg='#16213e')
        printer_info_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(printer_info_frame, text="الطابعة الحالية:",
                bg='#16213e', fg='white',
                font=('Segoe UI', 12)).pack(side='left')
        
        self.printer_var = tk.StringVar(value=self.default_printer)
        printer_combo = ttk.Combobox(printer_info_frame, textvariable=self.printer_var,
                                    values=self.get_available_printers(),
                                    font=('Segoe UI', 11), width=30)
        printer_combo.pack(side='right', padx=(10, 0))
        
        # قسم المعاينة
        preview_frame = tk.Frame(main_frame, bg='#16213e')
        preview_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        tk.Label(preview_frame, text="👁️ معاينة الفاتورة",
                bg='#16213e', fg='white',
                font=('Segoe UI', 14, 'bold')).pack(pady=10)
        
        # منطقة المعاينة
        preview_container = tk.Frame(preview_frame, bg='white', relief='sunken', bd=2)
        preview_container.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        self.preview_text = tk.Text(preview_container, 
                                   font=('Courier New', 9),
                                   bg='white', fg='black',
                                   wrap='none', state='disabled')
        
        # شريط التمرير للمعاينة
        preview_scrollbar = ttk.Scrollbar(preview_container, orient='vertical', 
                                         command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        
        self.preview_text.pack(side='left', fill='both', expand=True)
        preview_scrollbar.pack(side='right', fill='y')
        
        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame, bg='#1a1a2e')
        buttons_frame.pack(fill='x', pady=10)
        
        # زر المعاينة
        preview_btn = tk.Button(buttons_frame, text="👁️ تحديث المعاينة",
                               command=self.update_preview,
                               bg='#3498db', fg='white',
                               font=('Segoe UI', 12, 'bold'),
                               relief='flat', cursor='hand2',
                               width=15, height=2)
        preview_btn.pack(side='left', padx=5)
        
        # زر الطباعة
        print_btn = tk.Button(buttons_frame, text="🖨️ طباعة",
                             command=lambda: self.print_receipt(invoice_number, items, total_amount, print_window),
                             bg='#27ae60', fg='white',
                             font=('Segoe UI', 12, 'bold'),
                             relief='flat', cursor='hand2',
                             width=15, height=2)
        print_btn.pack(side='left', padx=5)
        
        # زر حفظ كـ PDF
        pdf_btn = tk.Button(buttons_frame, text="📄 حفظ PDF",
                           command=lambda: self.save_as_pdf(invoice_number, items, total_amount),
                           bg='#e74c3c', fg='white',
                           font=('Segoe UI', 12, 'bold'),
                           relief='flat', cursor='hand2',
                           width=15, height=2)
        pdf_btn.pack(side='left', padx=5)
        
        # زر إغلاق
        close_btn = tk.Button(buttons_frame, text="❌ إغلاق",
                             command=print_window.destroy,
                             bg='#95a5a6', fg='white',
                             font=('Segoe UI', 12, 'bold'),
                             relief='flat', cursor='hand2',
                             width=15, height=2)
        close_btn.pack(side='right', padx=5)
        
        # حفظ البيانات للاستخدام في الوظائف الأخرى
        self.current_invoice = {
            'number': invoice_number,
            'items': items,
            'total': total_amount
        }
        
        # تحديث المعاينة الأولية
        self.update_preview()
    
    def update_preview(self):
        """تحديث معاينة الفاتورة"""
        if not hasattr(self, 'current_invoice'):
            return
            
        receipt_type = self.receipt_type_var.get()
        receipt_info = self.receipt_types[receipt_type]
        
        # إنشاء نص الفاتورة
        receipt_text = self.generate_receipt_text(
            self.current_invoice['number'],
            self.current_invoice['items'],
            self.current_invoice['total'],
            receipt_info
        )
        
        # تحديث المعاينة
        self.preview_text.config(state='normal')
        self.preview_text.delete('1.0', tk.END)
        self.preview_text.insert('1.0', receipt_text)
        self.preview_text.config(state='disabled')
    
    def generate_receipt_text(self, invoice_number, items, total_amount, receipt_info):
        """إنشاء نص الفاتورة حسب النوع"""
        chars_per_line = receipt_info['chars_per_line']
        
        # الحصول على إعدادات المحل
        settings = self.db.get_settings()
        shop_name = settings[1] if settings else "مقهى البلايستيشن"
        shop_address = settings[2] if settings else "الرياض - المملكة العربية السعودية"
        footer_message = settings[4] if settings else "نسعد بخدمتكم"
        
        lines = []
        
        # رأس الفاتورة
        lines.append("=" * chars_per_line)
        lines.append(shop_name.center(chars_per_line))
        lines.append(shop_address.center(chars_per_line))
        lines.append("=" * chars_per_line)
        lines.append("")
        
        # معلومات الفاتورة
        lines.append(f"رقم الفاتورة: {invoice_number}")
        lines.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        lines.append(f"الطابعة: {receipt_info['name']}")
        lines.append("-" * chars_per_line)
        
        # عناوين الجدول
        if receipt_info['width'] >= 80:  # A4 أو حراري كبير
            header = f"{'الصنف':<20} {'الكمية':>6} {'السعر':>8} {'المجموع':>10}"
            lines.append(header)
        else:  # حراري صغير
            lines.append("الصنف           الكمية  المجموع")
        
        lines.append("-" * chars_per_line)
        
        # الأصناف
        for item in items:
            name = item['name']
            if len(name) > 15:
                name = name[:12] + "..."
            
            if receipt_info['width'] >= 80:
                line = f"{name:<20} {item['quantity']:>6} {item['price']:>8.2f} {item['subtotal']:>10.2f}"
            else:
                line = f"{name:<15} {item['quantity']:>3} {item['subtotal']:>8.2f}"
            
            lines.append(line)
        
        # المجموع
        lines.append("-" * chars_per_line)
        total_line = f"المجموع الكلي: {total_amount:.2f} ريال"
        lines.append(total_line.center(chars_per_line))
        lines.append("=" * chars_per_line)
        lines.append("")
        lines.append(footer_message.center(chars_per_line))
        lines.append("شكراً لزيارتكم".center(chars_per_line))
        lines.append("")
        
        # إضافة مساحة للقطع (للطابعات الحرارية)
        if 'thermal' in receipt_info:
            lines.extend([""] * 3)
        
        return "\n".join(lines)
    
    def print_receipt(self, invoice_number, items, total_amount, window):
        """طباعة الفاتورة"""
        try:
            receipt_type = self.receipt_type_var.get()
            printer_name = self.printer_var.get()
            
            # إنشاء ملف نصي للطباعة
            receipt_text = self.generate_receipt_text(
                invoice_number, items, total_amount, 
                self.receipt_types[receipt_type]
            )
            
            # حفظ في ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, 
                                                   suffix='.txt', encoding='utf-8')
            temp_file.write(receipt_text)
            temp_file.close()
            
            # طباعة الملف
            if sys.platform.startswith('win'):
                if printer_name != "لا توجد طابعة افتراضية":
                    win32api.ShellExecute(0, "print", temp_file.name, f'/d:"{printer_name}"', ".", 0)
                else:
                    os.startfile(temp_file.name, "print")
            else:
                subprocess.run(["lp", temp_file.name])
            
            messagebox.showinfo("نجح", f"تم إرسال الفاتورة للطباعة على {printer_name}")
            window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الطباعة: {str(e)}")
    
    def save_as_pdf(self, invoice_number, items, total_amount):
        """حفظ الفاتورة كـ PDF"""
        try:
            filename = filedialog.asksaveasfilename(
                title="حفظ الفاتورة",
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                initialname=f"فاتورة_{invoice_number}.pdf"
            )
            
            if filename:
                # هنا يمكن إضافة كود إنشاء PDF
                # للآن سنحفظ كملف نصي
                text_filename = filename.replace('.pdf', '.txt')
                receipt_text = self.generate_receipt_text(
                    invoice_number, items, total_amount,
                    self.receipt_types[self.receipt_type_var.get()]
                )
                
                with open(text_filename, 'w', encoding='utf-8') as f:
                    f.write(receipt_text)
                
                messagebox.showinfo("نجح", f"تم حفظ الفاتورة في:\n{text_filename}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
    
    def create_receipt_image(self, invoice_number, items, total_amount, receipt_type):
        """إنشاء صورة للفاتورة"""
        receipt_info = self.receipt_types[receipt_type]
        
        # حساب أبعاد الصورة
        if receipt_type == 'a4':
            img_width = 600
            img_height = 800
        else:
            img_width = int(receipt_info['width'] * 8)  # تحويل مم إلى بكسل
            img_height = 600 + (len(items) * 25)
        
        # إنشاء الصورة
        img = Image.new('RGB', (img_width, img_height), 'white')
        draw = ImageDraw.Draw(img)
        
        # محاولة تحميل خط
        try:
            font_large = ImageFont.truetype("arial.ttf", 16)
            font_medium = ImageFont.truetype("arial.ttf", 12)
            font_small = ImageFont.truetype("arial.ttf", 10)
        except:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # رسم محتوى الفاتورة
        y = 20
        
        # اسم المحل
        settings = self.db.get_settings()
        shop_name = settings[1] if settings else "مقهى البلايستيشن"
        
        text_width = draw.textlength(shop_name, font=font_large)
        x = (img_width - text_width) // 2
        draw.text((x, y), shop_name, fill='black', font=font_large)
        y += 30
        
        # باقي محتوى الفاتورة...
        # (يمكن إكمال هذا الجزء لاحقاً)
        
        return img
