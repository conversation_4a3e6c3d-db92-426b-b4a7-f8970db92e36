#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام نقاط البيع - واجهة الويب
Start Web POS System
"""

import os
import sys
import time
import threading
import webbrowser
from datetime import datetime

def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🎮 نظام نقاط البيع المتقدم - واجهة الويب 🎮              ║
    ║           PlayStation Cafe Advanced Web POS                  ║
    ║                                                              ║
    ║    ✨ الإصدار 2.0 - واجهة ويب عصرية ومتجاوبة ✨            ║
    ║                                                              ║
    ║    📅 التاريخ: {date}                                        ║
    ║    ⏰ الوقت: {time}                                          ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """.format(
        date=datetime.now().strftime("%Y-%m-%d"),
        time=datetime.now().strftime("%H:%M:%S")
    )
    print(banner)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # فحص Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # فحص الملفات المطلوبة
    required_files = [
        'web_interface.html',
        'app.js',
        'advanced_features.js',
        'web_server.py',
        'invoice_template.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة متوفرة")
    
    # فحص المكتبات
    try:
        import sqlite3
        print("✅ sqlite3 متوفر")
    except ImportError:
        print("❌ sqlite3 مفقود")
        return False
    
    try:
        import json
        print("✅ json متوفر")
    except ImportError:
        print("❌ json مفقود")
        return False
    
    return True

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "نظام نقاط البيع.lnk")
        target = sys.executable
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.Arguments = os.path.join(wDir, 'start_web_pos.py')
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ تم إنشاء اختصار على سطح المكتب")
    except:
        print("ℹ️ لم يتم إنشاء اختصار سطح المكتب")

def start_server():
    """بدء الخادم"""
    try:
        from web_server import POSWebServer
        
        print("🚀 بدء خادم الويب...")
        server = POSWebServer(port=8080)
        
        if server.start():
            print("✅ تم بدء الخادم بنجاح")
            print("🌐 الرابط: http://localhost:8080")
            print("📱 يمكن الوصول من الجوال عبر IP الجهاز")
            
            # فتح المتصفح بعد ثانيتين
            def open_browser():
                time.sleep(2)
                try:
                    webbrowser.open('http://localhost:8080')
                    print("🌐 تم فتح المتصفح")
                except:
                    print("⚠️ لم يتم فتح المتصفح تلقائياً")
                    print("   يرجى فتح http://localhost:8080 يدوياً")
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            print("\n" + "="*60)
            print("🎮 نظام نقاط البيع جاهز للاستخدام!")
            print("="*60)
            print("📋 الميزات المتاحة:")
            print("   • واجهة ويب عصرية ومتجاوبة")
            print("   • إصدار وطباعة الفواتير")
            print("   • إدارة الأصناف والمخزون")
            print("   • تقارير وإحصائيات مفصلة")
            print("   • تخصيص تصميم الفاتورة")
            print("   • إعدادات طباعة متقدمة")
            print("   • نسخ احتياطية وتصدير البيانات")
            print("="*60)
            print("⚠️ لإيقاف الخادم: اضغط Ctrl+C")
            print("="*60)
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 إيقاف الخادم...")
                server.stop()
                print("✅ تم إيقاف الخادم بنجاح")
                
        else:
            print("❌ فشل في بدء الخادم")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 تأكد من وجود ملف web_server.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")
        return False
    
    return True

def show_help():
    """عرض المساعدة"""
    help_text = """
    📖 دليل الاستخدام السريع:
    
    🖥️ الواجهة الرئيسية:
    • لوحة التحكم: عرض الإحصائيات والنظرة العامة
    • إصدار فاتورة: إنشاء فواتير جديدة
    • إدارة الأصناف: إضافة وتعديل المنتجات
    • التقارير: عرض تقارير المبيعات
    • الإعدادات: تخصيص إعدادات النظام
    
    🎨 تصميم الفاتورة:
    • تخطيط الفاتورة: تحديد أبعاد ونوع الورق
    • محتوى الفاتورة: تخصيص المعلومات المعروضة
    • الألوان والخطوط: تخصيص المظهر
    • معاينة: عرض الفاتورة قبل الطباعة
    
    🖨️ إعدادات الطباعة:
    • اختيار الطابعة: تحديد الطابعة الافتراضية
    • خيارات الطباعة: جودة وسرعة الطباعة
    • أنواع الفواتير: حراري 58/70/80 مم أو A4
    • اختبار الطباعة: فحص عمل الطابعة
    
    🔧 نصائح:
    • استخدم Ctrl+F للبحث السريع
    • يمكن الوصول من أي جهاز في الشبكة
    • النظام يحفظ البيانات تلقائياً
    • يمكن تصدير البيانات كنسخة احتياطية
    """
    print(help_text)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        print("💡 تأكد من تثبيت Python وجميع الملفات المطلوبة")
        input("اضغط Enter للخروج...")
        return
    
    print("\n✅ جميع المتطلبات متوفرة")
    
    # عرض الخيارات
    while True:
        print("\n" + "="*50)
        print("🎮 نظام نقاط البيع - واجهة الويب")
        print("="*50)
        print("1. 🚀 تشغيل النظام")
        print("2. 📖 عرض المساعدة")
        print("3. 🔗 إنشاء اختصار سطح المكتب")
        print("4. ❌ خروج")
        print("="*50)
        
        choice = input("اختر رقماً (1-4): ").strip()
        
        if choice == '1':
            if start_server():
                break
        elif choice == '2':
            show_help()
        elif choice == '3':
            create_desktop_shortcut()
        elif choice == '4':
            print("👋 شكراً لاستخدام نظام نقاط البيع")
            break
        else:
            print("⚠️ اختيار غير صحيح، يرجى المحاولة مرة أخرى")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
        print("💡 يرجى التأكد من سلامة الملفات وإعادة المحاولة")
    finally:
        input("\nاضغط Enter للخروج...")
