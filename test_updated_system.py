#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحدث مع إعدادات الطباعة والتصميم
Test Updated System with Print and Design Settings

فكرة وتنفيذ: المهندس سيف رافع
هذا البرنامج مجاني لوجه الله تعالى
"""

import os
import json
import webbrowser
import threading
import time
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler

class UpdatedPOSHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path == '/':
            self.path = '/web_interface.html'
        elif self.path.startswith('/api/'):
            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            if self.path == '/api/data':
                data = {
                    'items': [
                        {'id': 1, 'name': 'ساعة بلايستيشن', 'price': 15.0, 'category': 'ألعاب'},
                        {'id': 2, 'name': 'قهوة عربية', 'price': 8.0, 'category': 'مشروبات'},
                        {'id': 3, 'name': 'شاي أحمر', 'price': 5.0, 'category': 'مشروبات'},
                        {'id': 4, 'name': 'عصير برتقال', 'price': 10.0, 'category': 'مشروبات'},
                        {'id': 5, 'name': 'ساندويش تونة', 'price': 20.0, 'category': 'طعام'},
                        {'id': 6, 'name': 'بيتزا صغيرة', 'price': 35.0, 'category': 'طعام'},
                        {'id': 7, 'name': 'مياه معدنية', 'price': 2.0, 'category': 'مشروبات'},
                        {'id': 8, 'name': 'كولا', 'price': 6.0, 'category': 'مشروبات'},
                        {'id': 9, 'name': 'شيبس', 'price': 4.0, 'category': 'وجبات خفيفة'},
                        {'id': 10, 'name': 'شوكولاتة', 'price': 7.0, 'category': 'حلويات'}
                    ],
                    'stats': {
                        'today_invoices': 18,
                        'today_sales': 520.75,
                        'total_items': 10,
                        'avg_invoice': 28.93
                    },
                    'settings': {
                        'shop_name': 'مقهى البلايستيشن',
                        'shop_address': 'الرياض - المملكة العربية السعودية',
                        'shop_phone': '+966 11 234 5678',
                        'shop_email': '<EMAIL>',
                        'footer_message': 'نسعد بخدمتكم\nشكراً لزيارتكم'
                    }
                }
            elif self.path == '/api/printers':
                data = {
                    'printers': [
                        {'name': 'طابعة حرارية 58مم - USB', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة حرارية 80مم - USB', 'is_default': True, 'status': 'online'},
                        {'name': 'Microsoft Print to PDF', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة الشبكة HP LaserJet', 'is_default': False, 'status': 'online'},
                        {'name': 'طابعة Canon', 'is_default': False, 'status': 'offline'}
                    ]
                }
            else:
                data = {'success': True, 'message': 'تم تنفيذ العملية بنجاح'}
            
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            return
        
        return super().do_GET()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
                action = data.get('action', 'unknown')
                payload = data.get('data', {})
                
                # طباعة تفاصيل العملية مع التركيز على إعدادات التصميم والطباعة
                if action == 'save_design':
                    print(f"🎨 حفظ تصميم الفاتورة:")
                    print(f"   • حجم الورق: {payload.get('paperType', 'غير محدد')}")
                    print(f"   • موضع الشعار: {payload.get('logoPosition', 'غير محدد')}")
                    print(f"   • حجم الشعار: {payload.get('logoSize', 'غير محدد')}px")
                    print(f"   • حجم خط العنوان: {payload.get('titleFontSize', 'غير محدد')}px")
                    print(f"   • حجم خط المحتوى: {payload.get('contentFontSize', 'غير محدد')}px")
                    
                elif action == 'save_printer_settings':
                    print(f"🖨️ حفظ إعدادات الطباعة:")
                    print(f"   • نوع الفاتورة الافتراضي: {payload.get('defaultReceiptType', 'غير محدد')}")
                    print(f"   • الطابعة الافتراضية: {payload.get('defaultPrinter', 'غير محدد')}")
                    print(f"   • جودة الطباعة: {payload.get('printQuality', 'غير محدد')}")
                    print(f"   • عدد النسخ: {payload.get('printCopies', 'غير محدد')}")
                    
                elif action == 'print_invoice':
                    print(f"🖨️ طباعة فاتورة: {payload.get('number', 'غير محدد')}")
                    print(f"   • المبلغ: {payload.get('total', 0)} ريال")
                    print(f"   • عدد الأصناف: {len(payload.get('items', []))}")
                    
                elif action == 'save_invoice':
                    print(f"💾 حفظ فاتورة: {payload.get('number', 'غير محدد')} - المبلغ: {payload.get('total', 0)} ريال")
                    
                elif action == 'save_settings':
                    print(f"⚙️ حفظ إعدادات النظام")
                    if payload.get('shop_logo'):
                        print(f"   • تم حفظ شعار المحل")
                    
                elif action == 'add_item':
                    print(f"📦 إضافة صنف جديد: {payload.get('name', 'غير محدد')}")
                    
                else:
                    print(f"📝 إجراء: {action}")
                    
            except Exception as e:
                print(f"❌ خطأ في معالجة البيانات: {e}")
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = {'success': True, 'message': 'تم تنفيذ العملية بنجاح'}
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🎮 نظام نقاط البيع المحدث - اختبار الطباعة والتصميم 🎮    ║
    ║         Updated POS System - Print & Design Test            ║
    ║                                                              ║
    ║    ✨ الإصدار الأول - النسخة الأولى من برنامج الفواتير ✨     ║
    ║                                                              ║
    ║    👨‍💻 فكرة وتنفيذ: المهندس سيف رافع                        ║
    ║    💝 هذا البرنامج مجاني لوجه الله تعالى                     ║
    ║                                                              ║
    ║    📅 التاريخ: {date}                                        ║
    ║    ⏰ الوقت: {time}                                          ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """.format(
        date=datetime.now().strftime("%Y-%m-%d"),
        time=datetime.now().strftime("%H:%M:%S")
    )
    print(banner)

def start_server(port=8080):
    """بدء الخادم"""
    try:
        server = HTTPServer(('localhost', port), UpdatedPOSHandler)
        print(f"🌐 الخادم يعمل على: http://localhost:{port}")
        
        # فتح المتصفح بعد ثانيتين
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print("⚠️ لم يتم فتح المتصفح تلقائياً")
                print(f"   يرجى فتح الرابط يدوياً: http://localhost:{port}")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n" + "="*80)
        print("🎮 نظام نقاط البيع المحدث جاهز للاختبار!")
        print("="*80)
        print("🆕 التحديثات الجديدة:")
        print("   • ✅ تطبيق حجم الورق المختار في الطباعة")
        print("   • ✅ تطبيق إعدادات التصميم في المعاينة والطباعة")
        print("   • ✅ شعار المحل مع خيارات المحاذاة المتقدمة")
        print("   • ✅ أحجام الخطوط والألوان حسب الإعدادات")
        print("   • ✅ تزامن إعدادات الطباعة مع التصميم")
        print("="*80)
        print("🧪 خطوات الاختبار:")
        print("   1. ارفع شعار المحل من تبويب 'الإعدادات'")
        print("   2. اختر حجم الورق من تبويب 'إعدادات الطباعة'")
        print("   3. خصص التصميم من تبويب 'تصميم الفاتورة'")
        print("   4. جرب خيارات محاذاة الشعار المختلفة")
        print("   5. استخدم 'معاينة الفاتورة' لرؤية النتيجة")
        print("   6. اطبع الفاتورة وتأكد من تطبيق الإعدادات")
        print("="*80)
        print("💡 نصائح الاختبار:")
        print("   • جرب أحجام ورق مختلفة (58مم، 70مم، 80مم، A4)")
        print("   • اختبر مواضع الشعار المختلفة")
        print("   • غير أحجام الخطوط والألوان")
        print("   • تأكد من تطابق المعاينة مع الطباعة")
        print("="*80)
        print("⚠️ لإيقاف الخادم: اضغط Ctrl+C")
        print("="*80)
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 إيقاف الخادم...")
        server.shutdown()
        server.server_close()
        print("✅ تم إيقاف الخادم بنجاح")
        print("\n🙏 شكراً لاختبار النظام المحدث")
        print("💝 نسأل الله أن ينفع به ويجعله في ميزان حسناتنا")
        print("👨‍💻 فكرة وتنفيذ: المهندس سيف رافع")
        
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print("🔍 فحص الملفات المحدثة...")
    
    required_files = [
        'web_interface.html',
        'app.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع الملفات المطلوبة متوفرة")
    print("🚀 بدء اختبار النظام المحدث...")
    
    try:
        start_server()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
