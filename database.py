import sqlite3
import os
from datetime import datetime

class Database:
    def __init__(self, db_name="cafe_pos.db"):
        self.db_name = db_name
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول الأساسية"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # جدول الأصناف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                category TEXT DEFAULT 'عام',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                total_amount REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تفاصيل الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER,
                item_name TEXT NOT NULL,
                item_price REAL NOT NULL,
                quantity INTEGER NOT NULL,
                subtotal REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            )
        ''')
        
        # جدول الإعدادات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                shop_name TEXT DEFAULT 'مقهى البلايستيشن',
                shop_address TEXT DEFAULT 'العنوان',
                logo_path TEXT DEFAULT '',
                footer_message TEXT DEFAULT 'نسعد بخدمتكم',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج إعدادات افتراضية إذا لم تكن موجودة
        cursor.execute('SELECT COUNT(*) FROM settings')
        if cursor.fetchone()[0] == 0:
            cursor.execute('''
                INSERT INTO settings (shop_name, shop_address, footer_message)
                VALUES (?, ?, ?)
            ''', ('مقهى البلايستيشن', 'الرياض - المملكة العربية السعودية', 'نسعد بخدمتكم'))
        
        # إدراج بعض الأصناف الافتراضية
        cursor.execute('SELECT COUNT(*) FROM items')
        if cursor.fetchone()[0] == 0:
            default_items = [
                ('ساعة بلايستيشن', 15.0, 'ألعاب'),
                ('شاي', 5.0, 'مشروبات'),
                ('قهوة', 8.0, 'مشروبات'),
                ('عصير', 10.0, 'مشروبات'),
                ('ساندويش', 20.0, 'طعام'),
                ('بيتزا صغيرة', 35.0, 'طعام')
            ]
            cursor.executemany('INSERT INTO items (name, price, category) VALUES (?, ?, ?)', default_items)
        
        conn.commit()
        conn.close()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_name)
    
    def add_item(self, name, price, category='عام'):
        """إضافة صنف جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('INSERT INTO items (name, price, category) VALUES (?, ?, ?)', 
                      (name, price, category))
        conn.commit()
        conn.close()
    
    def get_all_items(self):
        """الحصول على جميع الأصناف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM items ORDER BY category, name')
        items = cursor.fetchall()
        conn.close()
        return items
    
    def update_item(self, item_id, name, price, category):
        """تحديث صنف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('UPDATE items SET name=?, price=?, category=? WHERE id=?', 
                      (name, price, category, item_id))
        conn.commit()
        conn.close()
    
    def delete_item(self, item_id):
        """حذف صنف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM items WHERE id=?', (item_id,))
        conn.commit()
        conn.close()
    
    def save_invoice(self, invoice_number, items_list, total_amount):
        """حفظ فاتورة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # حفظ الفاتورة الرئيسية
        cursor.execute('INSERT INTO invoices (invoice_number, total_amount) VALUES (?, ?)', 
                      (invoice_number, total_amount))
        invoice_id = cursor.lastrowid
        
        # حفظ تفاصيل الفاتورة
        for item in items_list:
            cursor.execute('''INSERT INTO invoice_items 
                            (invoice_id, item_name, item_price, quantity, subtotal) 
                            VALUES (?, ?, ?, ?, ?)''', 
                          (invoice_id, item['name'], item['price'], item['quantity'], item['subtotal']))
        
        conn.commit()
        conn.close()
        return invoice_id
    
    def get_settings(self):
        """الحصول على إعدادات المحل"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM settings ORDER BY id DESC LIMIT 1')
        settings = cursor.fetchone()
        conn.close()
        return settings
    
    def update_settings(self, shop_name, shop_address, logo_path, footer_message):
        """تحديث إعدادات المحل"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''UPDATE settings SET shop_name=?, shop_address=?, logo_path=?, 
                         footer_message=?, updated_at=CURRENT_TIMESTAMP WHERE id=1''', 
                      (shop_name, shop_address, logo_path, footer_message))
        conn.commit()
        conn.close()
    
    def get_daily_stats(self, date=None):
        """الحصول على إحصائيات يومية"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''SELECT COUNT(*) as invoice_count, SUM(total_amount) as total_sales 
                         FROM invoices WHERE DATE(created_at) = ?''', (date,))
        stats = cursor.fetchone()
        conn.close()
        return stats
    
    def get_recent_invoices(self, limit=10):
        """الحصول على آخر الفواتير"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''SELECT invoice_number, total_amount, created_at 
                         FROM invoices ORDER BY created_at DESC LIMIT ?''', (limit,))
        invoices = cursor.fetchall()
        conn.close()
        return invoices
