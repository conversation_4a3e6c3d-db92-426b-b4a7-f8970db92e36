#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إنشاء ملف exe للبرنامج
Build script for creating executable file

استخدام:
Usage: python build_exe.py
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """فحص وجود PyInstaller"""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("تثبيت PyInstaller...")
    print("Installing PyInstaller...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller==5.13.2'])
        print("✅ تم تثبيت PyInstaller بنجاح!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت PyInstaller: {e}")
        return False

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('logos', 'logos'),
        ('receipts', 'receipts'),
        ('exports', 'exports'),
        ('backups', 'backups'),
    ],
    hiddenimports=[
        'PIL._tkinter_finder',
        'arabic_reshaper',
        'bidi',
        'reportlab.pdfbase.ttfonts',
        'reportlab.pdfbase.pdfmetrics',
        'reportlab.lib.fonts',
        'sqlite3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CafePOS',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('cafe_pos.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف cafe_pos.spec")

def create_icon():
    """إنشاء أيقونة للبرنامج"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # إنشاء صورة للأيقونة
        size = (256, 256)
        img = Image.new('RGB', size, '#1a1a2e')
        draw = ImageDraw.Draw(img)
        
        # رسم دائرة
        circle_color = '#0f3460'
        margin = 20
        draw.ellipse([margin, margin, size[0]-margin, size[1]-margin], fill=circle_color)
        
        # إضافة نص
        try:
            font = ImageFont.truetype("arial.ttf", 60)
        except:
            font = ImageFont.load_default()
        
        text = "POS"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size[0] - text_width) // 2
        y = (size[1] - text_height) // 2
        
        draw.text((x, y), text, fill='white', font=font)
        
        # حفظ كـ ICO
        img.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ تم إنشاء أيقونة البرنامج")
        
    except Exception as e:
        print(f"⚠️ لا يمكن إنشاء الأيقونة: {e}")

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بدء بناء الملف التنفيذي...")
    print("Building executable...")
    
    try:
        # تنظيف الملفات السابقة
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        if os.path.exists('build'):
            shutil.rmtree('build')
        
        # تشغيل PyInstaller
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'cafe_pos.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            print("Executable built successfully!")
            
            # التحقق من وجود الملف
            exe_path = os.path.join('dist', 'CafePOS.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 مسار الملف: {exe_path}")
                print(f"📊 حجم الملف: {file_size:.1f} MB")
                
                # إنشاء مجلد التوزيع
                dist_folder = 'CafePOS_Distribution'
                if os.path.exists(dist_folder):
                    shutil.rmtree(dist_folder)
                os.makedirs(dist_folder)
                
                # نسخ الملف التنفيذي
                shutil.copy2(exe_path, dist_folder)
                
                # إنشاء ملف README
                readme_content = """
🎮 نظام نقاط البيع - مقهى البلايستيشن
PlayStation Cafe POS System

📋 تعليمات التشغيل:
1. قم بتشغيل ملف CafePOS.exe
2. سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
3. يمكنك إضافة الأصناف من قائمة "إدارة الأصناف"
4. لإصدار فاتورة جديدة، اختر "إصدار فاتورة جديدة"
5. يمكنك تخصيص إعدادات المحل من قائمة "الإعدادات"

🔧 الميزات:
✅ إصدار فواتير حرارية
✅ إدارة الأصناف والأسعار
✅ التقارير والإحصائيات
✅ إعدادات المحل المخصصة
✅ واجهة عصرية وسهلة الاستخدام

📞 للدعم الفني:
يرجى التواصل مع المطور

تم التطوير بواسطة: مساعد الذكي الاصطناعي
Developed by: AI Assistant
                """
                
                with open(os.path.join(dist_folder, 'README.txt'), 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                
                print(f"📦 تم إنشاء مجلد التوزيع: {dist_folder}")
                print("Distribution folder created!")
                
            return True
        else:
            print("❌ فشل في بناء الملف التنفيذي")
            print("Failed to build executable")
            print("خطأ:", result.stderr)
            print("Error:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء الملف التنفيذي: {e}")
        return False

def create_installer_script():
    """إنشاء سكريبت تثبيت"""
    installer_content = '''@echo off
echo 🎮 مرحباً بك في برنامج نقاط البيع - مقهى البلايستيشن
echo Welcome to PlayStation Cafe POS System
echo.

echo 📋 جاري التحقق من المتطلبات...
echo Checking requirements...

if not exist "CafePOS.exe" (
    echo ❌ ملف CafePOS.exe غير موجود
    echo CafePOS.exe file not found
    pause
    exit /b 1
)

echo ✅ تم العثور على الملف التنفيذي
echo Executable file found

echo.
echo 🚀 لتشغيل البرنامج، انقر نقراً مزدوجاً على CafePOS.exe
echo To run the program, double-click on CafePOS.exe
echo.

echo 📁 يمكنك إنشاء اختصار على سطح المكتب
echo You can create a desktop shortcut

pause
'''
    
    with open('CafePOS_Distribution/install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ تم إنشاء سكريبت التثبيت")

def main():
    """الدالة الرئيسية"""
    print("🎮 أداة بناء نظام نقاط البيع - مقهى البلايستيشن")
    print("PlayStation Cafe POS System Build Tool")
    print("=" * 50)
    
    # فحص PyInstaller
    if not check_pyinstaller():
        print("PyInstaller غير مثبت. جاري التثبيت...")
        if not install_pyinstaller():
            return
    
    # إنشاء المجلدات المطلوبة
    directories = ['logos', 'receipts', 'exports', 'backups']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ تم إنشاء مجلد {directory}")
    
    # إنشاء الأيقونة
    create_icon()
    
    # إنشاء ملف .spec
    create_spec_file()
    
    # بناء الملف التنفيذي
    if build_executable():
        # إنشاء سكريبت التثبيت
        create_installer_script()
        
        print("\n" + "=" * 50)
        print("🎉 تم بناء البرنامج بنجاح!")
        print("Program built successfully!")
        print("\n📁 ستجد الملف التنفيذي في مجلد: CafePOS_Distribution")
        print("You will find the executable in folder: CafePOS_Distribution")
        print("\n🚀 يمكنك الآن توزيع البرنامج!")
        print("You can now distribute the program!")
    else:
        print("\n❌ فشل في بناء البرنامج")
        print("Failed to build the program")

if __name__ == "__main__":
    main()
