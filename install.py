#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تثبيت المكتبات المطلوبة لنظام نقاط البيع
Installation script for POS System dependencies
"""

import sys
import subprocess
import os

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🎮 نظام نقاط البيع - مقهى البلايستيشن")
    print("PlayStation Cafe POS System")
    print("=" * 60)
    print("📦 أداة تثبيت المكتبات المطلوبة")
    print("Dependencies Installation Tool")
    print("=" * 60)

def check_python_version():
    """فحص إصدار Python"""
    version = sys.version_info
    print(f"🐍 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print("❌ Python 3.7+ is required")
        return False
    
    print("✅ إصدار Python متوافق")
    return True

def check_pip():
    """فحص وجود pip"""
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ pip متوفر")
            return True
        else:
            print("❌ pip غير متوفر")
            return False
    except Exception:
        print("❌ خطأ في فحص pip")
        return False

def install_package(package_name, display_name=None):
    """تثبيت مكتبة واحدة"""
    if display_name is None:
        display_name = package_name
    
    print(f"\n📦 تثبيت {display_name}...")
    print(f"Installing {display_name}...")
    
    try:
        # محاولة التثبيت
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            package_name, '--upgrade'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ تم تثبيت {display_name} بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت {display_name}")
            if result.stderr:
                print(f"خطأ: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ انتهت مهلة تثبيت {display_name}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {display_name}: {e}")
        return False

def test_imports():
    """اختبار استيراد المكتبات"""
    print("\n🧪 اختبار المكتبات المثبتة...")
    print("Testing installed packages...")
    
    tests = [
        ('tkinter', 'import tkinter as tk'),
        ('sqlite3', 'import sqlite3'),
        ('PIL (Pillow)', 'from PIL import Image'),
        ('reportlab', 'from reportlab.pdfgen import canvas'),
        ('arabic_reshaper', 'import arabic_reshaper'),
        ('python-bidi', 'from bidi.algorithm import get_display')
    ]
    
    success_count = 0
    
    for name, import_code in tests:
        try:
            exec(import_code)
            print(f"✅ {name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name}: {e}")
        except Exception as e:
            print(f"⚠️ {name}: {e}")
    
    print(f"\n📊 النتيجة: {success_count}/{len(tests)} مكتبات تعمل بشكل صحيح")
    return success_count == len(tests)

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص Python
    if not check_python_version():
        input("\n⏸️ اضغط Enter للخروج...")
        return
    
    # فحص pip
    if not check_pip():
        print("\n💡 يرجى تثبيت pip أولاً")
        input("\n⏸️ اضغط Enter للخروج...")
        return
    
    # قائمة المكتبات المطلوبة
    packages = [
        ('Pillow', 'PIL (معالجة الصور)'),
        ('reportlab', 'ReportLab (إنشاء PDF)'),
        ('arabic-reshaper', 'Arabic Reshaper (دعم العربية)'),
        ('python-bidi', 'Python BiDi (اتجاه النص)')
    ]
    
    print(f"\n📋 سيتم تثبيت {len(packages)} مكتبات:")
    for package, desc in packages:
        print(f"   • {package} - {desc}")
    
    # تأكيد المستخدم
    response = input("\n❓ هل تريد المتابعة؟ (y/n): ").lower().strip()
    if response not in ['y', 'yes', 'نعم', 'ن']:
        print("تم الإلغاء")
        return
    
    # تثبيت المكتبات
    print("\n🚀 بدء التثبيت...")
    success_count = 0
    
    for package, desc in packages:
        if install_package(package, desc):
            success_count += 1
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج التثبيت:")
    print(f"✅ نجح: {success_count}")
    print(f"❌ فشل: {len(packages) - success_count}")
    
    if success_count == len(packages):
        print("\n🎉 تم تثبيت جميع المكتبات بنجاح!")
        
        # اختبار الاستيراد
        if test_imports():
            print("\n✅ جميع المكتبات تعمل بشكل صحيح!")
            print("\n🚀 يمكنك الآن تشغيل البرنامج:")
            print("   python run.py")
        else:
            print("\n⚠️ بعض المكتبات لا تعمل بشكل صحيح")
    else:
        print("\n❌ فشل في تثبيت بعض المكتبات")
        print("\n💡 يمكنك المحاولة مرة أخرى أو تثبيت المكتبات يدوياً:")
        for package, _ in packages:
            print(f"   pip install {package}")
    
    print("\n" + "=" * 60)
    input("⏸️ اضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف التثبيت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("\n⏸️ اضغط Enter للخروج...")
