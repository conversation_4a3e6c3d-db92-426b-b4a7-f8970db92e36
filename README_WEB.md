# 🎮 نظام نقاط البيع المتقدم - واجهة الويب

## نظام نقاط بيع عصري ومتكامل مع واجهة ويب جميلة ومتجاوبة

---

## 📋 نظرة عامة

نظام نقاط البيع المتقدم هو حل شامل ومتكامل مصمم خصيصاً للمقاهي ومحلات الألعاب. يتميز النظام بواجهة ويب عصرية وسهلة الاستخدام، مع إمكانيات طباعة متقدمة وتخصيص كامل للفواتير.

### ✨ الميزات الرئيسية

- 🌐 **واجهة ويب عصرية**: تصميم متجاوب يعمل على جميع الأجهزة
- 🖨️ **طباعة متقدمة**: دعم الطابعات الحرارية وA4 مع تخصيص كامل
- 🎨 **تصميم الفاتورة**: إمكانية تخصيص شكل ومحتوى الفاتورة
- 📊 **تقارير شاملة**: إحصائيات مفصلة ورسوم بيانية
- 💾 **نسخ احتياطية**: حفظ واستعادة البيانات تلقائياً
- 📱 **متجاوب**: يعمل على الكمبيوتر والجوال والتابلت
- 🔒 **آمن**: حماية البيانات وتشفير المعلومات الحساسة

---

## 🚀 التثبيت والتشغيل

### المتطلبات

- Python 3.6 أو أحدث
- متصفح ويب حديث (Chrome, Firefox, Edge, Safari)
- نظام التشغيل: Windows, macOS, Linux

### خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   # تأكد من وجود جميع الملفات في نفس المجلد
   ```

2. **تشغيل النظام**
   ```bash
   python start_web_pos.py
   ```

3. **فتح المتصفح**
   - سيتم فتح المتصفح تلقائياً على `http://localhost:8080`
   - أو افتح الرابط يدوياً في المتصفح

### التشغيل السريع

```bash
# تشغيل مباشر
python web_server.py

# أو استخدام ملف التشغيل المتقدم
python start_web_pos.py
```

---

## 📁 ملفات النظام

### الملفات الأساسية
- `web_interface.html` - الواجهة الرئيسية
- `app.js` - JavaScript الأساسي
- `advanced_features.js` - الميزات المتقدمة
- `web_server.py` - خادم الويب
- `start_web_pos.py` - ملف التشغيل

### ملفات التخصيص
- `invoice_design.html` - صفحة تصميم الفاتورة
- `printer_settings.html` - إعدادات الطباعة
- `invoice_template.html` - قالب الفاتورة للطباعة

### قاعدة البيانات
- `pos_web.db` - قاعدة البيانات (تُنشأ تلقائياً)

---

## 🎯 الاستخدام

### 1. لوحة التحكم
- عرض الإحصائيات اليومية والشهرية
- نظرة سريعة على المبيعات والأرباح
- الأصناف الأكثر مبيعاً

### 2. إصدار الفواتير
- إضافة الأصناف بسهولة
- حساب المجموع تلقائياً
- طباعة فورية أو حفظ للطباعة لاحقاً

### 3. إدارة الأصناف
- إضافة وتعديل وحذف الأصناف
- تصنيف المنتجات
- تحديد الأسعار والكميات

### 4. تخصيص الفاتورة
- اختيار نوع الورق (حراري 58/70/80 مم أو A4)
- تخصيص الألوان والخطوط
- إضافة شعار المحل
- تعديل رسائل الترحيب والختام

### 5. إعدادات الطباعة
- اختيار الطابعة الافتراضية
- ضبط جودة وسرعة الطباعة
- اختبار الطباعة
- إعدادات متقدمة للطابعات الحرارية

---

## 🛠️ الإعدادات المتقدمة

### إعدادات المحل
```javascript
{
  "shop_name": "مقهى البلايستيشن",
  "shop_address": "الرياض - المملكة العربية السعودية",
  "shop_phone": "+966 11 234 5678",
  "shop_email": "<EMAIL>",
  "footer_message": "نسعد بخدمتكم"
}
```

### إعدادات الطباعة
```javascript
{
  "default_printer": "طابعة حرارية - USB",
  "default_receipt_type": "thermal_80",
  "auto_print": false,
  "print_copies": 1,
  "print_quality": "normal"
}
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. لا يمكن الوصول للموقع
```bash
# تأكد من تشغيل الخادم
python web_server.py

# تحقق من المنفذ
netstat -an | findstr :8080
```

#### 2. مشاكل الطباعة
- تأكد من تثبيت تعريفات الطابعة
- فحص اتصال الطابعة
- تشغيل اختبار الطباعة من الإعدادات

#### 3. مشاكل قاعدة البيانات
```python
# إعادة إنشاء قاعدة البيانات
import os
if os.path.exists('pos_web.db'):
    os.remove('pos_web.db')
# ثم إعادة تشغيل النظام
```

---

## 📊 قاعدة البيانات

### جداول النظام

#### جدول الأصناف (items)
```sql
CREATE TABLE items (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    price REAL NOT NULL,
    category TEXT DEFAULT 'عام',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول الفواتير (invoices)
```sql
CREATE TABLE invoices (
    id INTEGER PRIMARY KEY,
    invoice_number TEXT UNIQUE NOT NULL,
    total_amount REAL NOT NULL,
    items_json TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول الإعدادات (settings)
```sql
CREATE TABLE settings (
    id INTEGER PRIMARY KEY,
    shop_name TEXT,
    shop_address TEXT,
    shop_phone TEXT,
    shop_email TEXT,
    footer_message TEXT,
    default_receipt_type TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔐 الأمان

### إجراءات الحماية
- تشفير البيانات الحساسة
- نسخ احتياطية تلقائية
- حماية من SQL Injection
- تنظيف المدخلات
- جلسات آمنة

---

## 📞 الدعم والمساعدة

### الأسئلة الشائعة
1. **كيف أغير لغة النظام؟** - النظام يدعم العربية افتراضياً
2. **هل يمكن استخدامه على أكثر من جهاز؟** - نعم، عبر الشبكة المحلية
3. **كيف أضيف طابعة جديدة؟** - من إعدادات الطباعة > اختيار الطابعة
4. **هل البيانات محفوظة؟** - نعم، مع نسخ احتياطية تلقائية

---

## 🎮 مميزات خاصة بمقاهي البلايستيشن

### إدارة الألعاب
- تتبع ساعات اللعب
- إدارة أجهزة البلايستيشن
- حجز الأوقات
- تسعير مرن حسب الوقت

### قوائم خاصة
- مشروبات الألعاب
- وجبات سريعة للاعبين
- عروض خاصة
- باقات مجمعة

### تقارير متخصصة
- أكثر الألعاب شعبية
- أوقات الذروة
- إيرادات الألعاب مقابل المأكولات
- تحليل سلوك العملاء

---

---

## 👨‍💻 المطور

**فكرة وتنفيذ: المهندس سيف رافع**

مطور برمجيات ومهندس أنظمة متخصص في تطوير الحلول التقنية للمشاريع الصغيرة والمتوسطة.

---

## 💝 رسالة خاصة

**هذا البرنامج مجاني لوجه الله تعالى**

نسأل الله أن ينفع به ويجعله في ميزان حسناتنا جميعاً. تم تطوير هذا النظام بهدف مساعدة أصحاب المشاريع الصغيرة في إدارة أعمالهم بكفاءة وسهولة.

*"وَمَن يَعْمَلْ مِثْقَالَ ذَرَّةٍ خَيْرًا يَرَهُ"* - صدق الله العظيم

---

## 🆕 الميزات الجديدة في هذا الإصدار

### 🎨 تخصيص متقدم للفواتير
- تصميم كامل للفاتورة مع معاينة مباشرة
- اختيار الألوان والخطوط
- تخصيص أبعاد الفاتورة
- إضافة شعار المحل

### 🖨️ نظام طباعة متطور
- معاينة الفاتورة قبل الطباعة
- دعم أنواع مختلفة من الورق
- اختبار الطباعة المتقدم
- إعدادات طباعة شاملة

### ⚙️ إعدادات محسنة
- رفع وإدارة شعار المحل
- معلومات مفصلة عن النظام
- إعدادات المحل المتقدمة
- حفظ تلقائي للإعدادات

---

**🎮 نظام نقاط البيع المتقدم - مقهى البلايستيشن**
*الإصدار الأول - النسخة الأولى من برنامج الفواتير*

**فكرة وتنفيذ: المهندس سيف رافع**
*هذا البرنامج مجاني لوجه الله تعالى*

© 2025 - جميع الحقوق محفوظة
